/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import * as fs from 'fs';
import { JSONPath } from 'jsonpath-plus';
import { Tool, ToolError } from './base';
import { ToolParameter, ToolCallArguments, ToolExecResult } from '../types/tool';

/**
 * <PERSON><PERSON> for editing JSON files using JSONPath expressions
 */
export class JSONEditTool extends Tool {
  getName(): string {
    return 'json_edit_tool';
  }

  getDescription(): string {
    return `Tool for editing JSON files with JSONPath expressions
* Supports targeted modifications to JSON structures using JSONPath syntax
* Operations: view, set, add, remove
* JSONPath examples: '$.users[0].name', '$.config.database.host', '$.items[*].price'
* Safe JSON parsing and validation with detailed error messages
* Preserves JSON formatting where possible`;
  }

  getParameters(): ToolParameter[] {
    return [
      {
        name: 'command',
        type: 'string',
        description: 'The command to execute',
        required: true,
        enum: ['view', 'set', 'add', 'remove'],
      },
      {
        name: 'path',
        type: 'string',
        description: 'The JSON file path',
        required: true,
      },
      {
        name: 'json_path',
        type: 'string',
        description: 'JSONPath expression for the target location',
        required: false,
      },
      {
        name: 'value',
        type: 'string',
        description: 'The value to set/add (JSON string)',
        required: false,
      },
    ];
  }

  async execute(arguments: ToolCallArguments): Promise<ToolExecResult> {
    this.validateArguments(arguments);

    const command = arguments.command as string;
    const filePath = arguments.path as string;
    const jsonPath = arguments.json_path as string;
    const value = arguments.value as string;

    try {
      switch (command) {
        case 'view':
          return this.viewHandler(filePath, jsonPath);
        case 'set':
          return this.setHandler(filePath, jsonPath, value);
        case 'add':
          return this.addHandler(filePath, jsonPath, value);
        case 'remove':
          return this.removeHandler(filePath, jsonPath);
        default:
          return {
            error: `Unknown command: ${command}`,
            errorCode: -1,
          };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        error: errorMessage,
        errorCode: -1,
      };
    }
  }

  private viewHandler(filePath: string, jsonPath?: string): ToolExecResult {
    try {
      if (!fs.existsSync(filePath)) {
        return {
          error: `File does not exist: ${filePath}`,
          errorCode: -1,
        };
      }

      const content = fs.readFileSync(filePath, 'utf-8');
      const jsonData = JSON.parse(content);

      if (!jsonPath) {
        return {
          output: `JSON content of ${filePath}:\n${JSON.stringify(jsonData, null, 2)}`,
        };
      }

      const result = JSONPath({ path: jsonPath, json: jsonData });
      
      return {
        output: `JSONPath query result for '${jsonPath}':\n${JSON.stringify(result, null, 2)}`,
      };
    } catch (error) {
      return {
        error: `Error viewing JSON: ${error}`,
        errorCode: -1,
      };
    }
  }

  private setHandler(filePath: string, jsonPath: string, value: string): ToolExecResult {
    if (!jsonPath || !value) {
      return {
        error: 'Parameters `json_path` and `value` are required for set command',
        errorCode: -1,
      };
    }

    try {
      if (!fs.existsSync(filePath)) {
        return {
          error: `File does not exist: ${filePath}`,
          errorCode: -1,
        };
      }

      const content = fs.readFileSync(filePath, 'utf-8');
      const jsonData = JSON.parse(content);
      const parsedValue = JSON.parse(value);

      // Use JSONPath to set the value
      const result = JSONPath({
        path: jsonPath,
        json: jsonData,
        value: parsedValue,
        resultType: 'value',
      });

      if (result.length === 0) {
        return {
          error: `JSONPath '${jsonPath}' did not match any elements`,
          errorCode: -1,
        };
      }

      fs.writeFileSync(filePath, JSON.stringify(jsonData, null, 2), 'utf-8');

      return {
        output: `Successfully set value at '${jsonPath}' in ${filePath}`,
      };
    } catch (error) {
      return {
        error: `Error setting JSON value: ${error}`,
        errorCode: -1,
      };
    }
  }

  private addHandler(filePath: string, jsonPath: string, value: string): ToolExecResult {
    if (!jsonPath || !value) {
      return {
        error: 'Parameters `json_path` and `value` are required for add command',
        errorCode: -1,
      };
    }

    try {
      if (!fs.existsSync(filePath)) {
        return {
          error: `File does not exist: ${filePath}`,
          errorCode: -1,
        };
      }

      const content = fs.readFileSync(filePath, 'utf-8');
      const jsonData = JSON.parse(content);
      const parsedValue = JSON.parse(value);

      // For arrays, push the value
      const target = JSONPath({ path: jsonPath, json: jsonData });
      
      if (target.length === 0) {
        return {
          error: `JSONPath '${jsonPath}' did not match any elements`,
          errorCode: -1,
        };
      }

      const targetValue = target[0];
      if (Array.isArray(targetValue)) {
        targetValue.push(parsedValue);
      } else if (typeof targetValue === 'object' && targetValue !== null) {
        // For objects, we need a key
        return {
          error: 'Adding to objects requires a specific key in the JSONPath',
          errorCode: -1,
        };
      } else {
        return {
          error: 'Target is not an array or object',
          errorCode: -1,
        };
      }

      fs.writeFileSync(filePath, JSON.stringify(jsonData, null, 2), 'utf-8');

      return {
        output: `Successfully added value to '${jsonPath}' in ${filePath}`,
      };
    } catch (error) {
      return {
        error: `Error adding JSON value: ${error}`,
        errorCode: -1,
      };
    }
  }

  private removeHandler(filePath: string, jsonPath: string): ToolExecResult {
    if (!jsonPath) {
      return {
        error: 'Parameter `json_path` is required for remove command',
        errorCode: -1,
      };
    }

    try {
      if (!fs.existsSync(filePath)) {
        return {
          error: `File does not exist: ${filePath}`,
          errorCode: -1,
        };
      }

      const content = fs.readFileSync(filePath, 'utf-8');
      const jsonData = JSON.parse(content);

      // This is a simplified implementation
      // In a full implementation, you'd need more sophisticated JSONPath removal
      const result = JSONPath({
        path: jsonPath,
        json: jsonData,
        callback: () => undefined, // Remove by returning undefined
      });

      if (result.length === 0) {
        return {
          error: `JSONPath '${jsonPath}' did not match any elements`,
          errorCode: -1,
        };
      }

      fs.writeFileSync(filePath, JSON.stringify(jsonData, null, 2), 'utf-8');

      return {
        output: `Successfully removed value at '${jsonPath}' from ${filePath}`,
      };
    } catch (error) {
      return {
        error: `Error removing JSON value: ${error}`,
        errorCode: -1,
      };
    }
  }
}
