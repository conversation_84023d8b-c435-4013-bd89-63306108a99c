/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { Tool } from './base';
import { ToolParameter, ToolCallArguments, ToolExecResult } from '../types/tool';

/**
 * Thought data structure
 */
interface ThoughtData {
  thought: string;
  nextThoughtNeeded: boolean;
  thoughtNumber: number;
  totalThoughts: number;
  isRevision?: boolean;
  revisesThought?: number;
  branchFromThought?: number;
  branchId?: string;
}

/**
 * A tool for sequential thinking that helps break down complex problems
 */
export class SequentialThinkingTool extends Tool {
  private thoughtHistory: ThoughtData[] = [];
  private branches: Record<string, ThoughtData[]> = {};

  getName(): string {
    return 'sequentialthinking';
  }

  getDescription(): string {
    return `A detailed tool for dynamic and reflective problem-solving through thoughts.
This tool helps analyze problems through a flexible thinking process that can adapt and evolve.
Each thought can build on, question, or revise previous insights as understanding deepens.

Key features:
- You can adjust total_thoughts up or down as you progress
- You can question or revise previous thoughts
- You can add more thoughts even after reaching what seemed like the end
- You can express uncertainty and explore alternative approaches
- Not every thought needs to build linearly - you can branch or backtrack
- Generates a solution hypothesis
- Verifies the hypothesis based on the Chain of Thought steps
- Repeats the process until satisfied
- Provides a correct answer

Parameters explained:
- thought: Your current thinking step, which can include:
  * Regular analytical steps
  * Revisions of previous thoughts
  * Questions about previous decisions
  * Realizations about needing more analysis
  * Changes in approach
  * Hypothesis generation
  * Hypothesis verification
- next_thought_needed: True if you need more thinking, even if at what seemed like the end
- thought_number: Current number in sequence (can go beyond initial total if needed)
- total_thoughts: Current estimate of thoughts needed (can be adjusted up/down)
- is_revision: A boolean indicating if this thought revises previous thinking
- revises_thought: If is_revision is true, which thought number is being reconsidered

You should:
1. Start with an initial estimate of needed thoughts, but be ready to adjust
2. Feel free to question or revise previous thoughts
3. Don't hesitate to add more thoughts if needed, even at the "end"
4. Express uncertainty when present
5. Mark thoughts that revise previous thinking or branch into new paths
6. Ignore information that is irrelevant to the current step
7. Generate a solution hypothesis when appropriate
8. Verify the hypothesis based on the Chain of Thought steps
9. Repeat the process until satisfied with the solution
10. Provide a single, ideally correct answer as the final output
11. Only set next_thought_needed to false when truly done and a satisfactory answer is reached`;
  }

  getParameters(): ToolParameter[] {
    return [
      {
        name: 'thought',
        type: 'string',
        description: 'Your current thinking step',
        required: true,
      },
      {
        name: 'next_thought_needed',
        type: 'boolean',
        description: 'Whether another thought step is needed',
        required: true,
      },
      {
        name: 'thought_number',
        type: 'number',
        description: 'Current thought number. Minimum value is 1.',
        required: true,
      },
      {
        name: 'total_thoughts',
        type: 'number',
        description: 'Estimated total thoughts needed. Minimum value is 1.',
        required: true,
      },
      {
        name: 'is_revision',
        type: 'boolean',
        description: 'Whether this revises previous thinking',
        required: false,
      },
      {
        name: 'revises_thought',
        type: 'number',
        description: 'Which thought number is being revised (if is_revision is true)',
        required: false,
      },
      {
        name: 'branch_from_thought',
        type: 'number',
        description: 'Thought number to branch from for alternative exploration',
        required: false,
      },
      {
        name: 'branch_id',
        type: 'string',
        description: 'Unique identifier for this branch',
        required: false,
      },
    ];
  }

  async execute(arguments: ToolCallArguments): Promise<ToolExecResult> {
    try {
      // Validate and extract thought data
      const validatedInput = this.validateThoughtData(arguments);

      // Adjust total thoughts if current thought number exceeds it
      if (validatedInput.thoughtNumber > validatedInput.totalThoughts) {
        validatedInput.totalThoughts = validatedInput.thoughtNumber;
      }

      // Add to thought history
      this.thoughtHistory.push(validatedInput);

      // Handle branching
      if (validatedInput.branchFromThought && validatedInput.branchId) {
        if (!this.branches[validatedInput.branchId]) {
          this.branches[validatedInput.branchId] = [];
        }
        this.branches[validatedInput.branchId].push(validatedInput);
      }

      // Prepare response
      const responseData = {
        thoughtNumber: validatedInput.thoughtNumber,
        totalThoughts: validatedInput.totalThoughts,
        nextThoughtNeeded: validatedInput.nextThoughtNeeded,
        branches: Object.keys(this.branches),
        thoughtHistoryLength: this.thoughtHistory.length,
      };

      return {
        output: `Sequential thinking step completed.\n\nStatus:\n${JSON.stringify(responseData, null, 2)}`,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        error: `Error in sequential thinking: ${errorMessage}`,
        errorCode: -1,
      };
    }
  }

  private validateThoughtData(arguments: ToolCallArguments): ThoughtData {
    const thought = arguments.thought as string;
    const nextThoughtNeeded = arguments.next_thought_needed as boolean;
    const thoughtNumber = arguments.thought_number as number;
    const totalThoughts = arguments.total_thoughts as number;
    const isRevision = arguments.is_revision as boolean | undefined;
    const revisesThought = arguments.revises_thought as number | undefined;
    const branchFromThought = arguments.branch_from_thought as number | undefined;
    const branchId = arguments.branch_id as string | undefined;

    // Validate required fields
    if (!thought || typeof thought !== 'string') {
      throw new Error('thought is required and must be a string');
    }

    if (typeof nextThoughtNeeded !== 'boolean') {
      throw new Error('next_thought_needed is required and must be a boolean');
    }

    if (typeof thoughtNumber !== 'number' || thoughtNumber < 1) {
      throw new Error('thought_number is required and must be a number >= 1');
    }

    if (typeof totalThoughts !== 'number' || totalThoughts < 1) {
      throw new Error('total_thoughts is required and must be a number >= 1');
    }

    // Validate revision fields
    if (isRevision && (typeof revisesThought !== 'number' || revisesThought < 1)) {
      throw new Error('revises_thought must be a valid thought number when is_revision is true');
    }

    // Validate branching fields
    if (branchFromThought && !branchId) {
      throw new Error('branch_id is required when branch_from_thought is specified');
    }

    if (branchId && !branchFromThought) {
      throw new Error('branch_from_thought is required when branch_id is specified');
    }

    return {
      thought,
      nextThoughtNeeded,
      thoughtNumber,
      totalThoughts,
      isRevision,
      revisesThought,
      branchFromThought,
      branchId,
    };
  }

  /**
   * Get the current thought history
   */
  getThoughtHistory(): ThoughtData[] {
    return [...this.thoughtHistory];
  }

  /**
   * Get all branches
   */
  getBranches(): Record<string, ThoughtData[]> {
    return { ...this.branches };
  }

  /**
   * Clear the thought history and branches
   */
  clearHistory(): void {
    this.thoughtHistory = [];
    this.branches = {};
  }
}
