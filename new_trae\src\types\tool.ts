/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

/**
 * Tool parameter definition
 */
export interface ToolParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description: string;
  required: boolean;
  enum?: string[];
  properties?: Record<string, ToolParameter>;
  items?: ToolParameter;
}

/**
 * Tool call arguments
 */
export type ToolCallArguments = Record<string, unknown>;

/**
 * Tool call structure
 */
export interface ToolCall {
  callId: string;
  id?: string; // For backward compatibility
  name: string;
  arguments: ToolCallArguments;
}

/**
 * Tool execution result
 */
export interface ToolExecResult {
  output?: string;
  error?: string;
  errorCode?: number;
}

/**
 * Tool result structure
 */
export interface ToolResult {
  callId: string;
  id?: string; // For backward compatibility
  name: string;
  result: ToolExecResult;
}

/**
 * Tool input schema for LLM
 */
export interface ToolInputSchema {
  type: 'object';
  properties: Record<string, unknown>;
  required: string[];
  additionalProperties?: boolean;
}

// CKG (Code Knowledge Graph) related types
export interface FunctionEntry {
  name: string;
  filePath: string;
  body: string;
  startLine: number;
  endLine: number;
  parentFunction?: FunctionEntry;
  parentClass?: ClassEntry;
}

export interface ClassEntry {
  name: string;
  filePath: string;
  body: string;
  fields: string[];
  methods: string[];
  startLine: number;
  endLine: number;
}

export interface CKGStorage {
  dbConnection: any; // sqlite3.Database
  codebaseSnapshotHash: string;
}

export type CKGCommand = 'search_function' | 'search_class' | 'search_class_method';
export type EntryType = 'functions' | 'classes' | 'class_methods';
