/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { loadConfig } from '../utils/config';
import { LLMProvider } from '../types/llm';

describe('Config', () => {
  beforeEach(() => {
    // Clear environment variables
    delete process.env.OPENAI_API_KEY;
    delete process.env.ANTHROPIC_API_KEY;
  });

  describe('loadConfig', () => {
    it('should load default configuration', () => {
      const config = loadConfig();
      
      expect(config.defaultProvider).toBe(LLMProvider.ANTHROPIC);
      expect(config.maxSteps).toBe(20);
      expect(config.enableLakeview).toBe(true);
      expect(config.modelProviders).toBeDefined();
    });

    it('should override provider and model', () => {
      const config = loadConfig({
        provider: LLMProvider.OPENAI,
        model: 'gpt-4',
        apiKey: 'test-key',
      });
      
      expect(config.defaultProvider).toBe(LLMProvider.OPENAI);
      expect(config.modelProviders[LLMProvider.OPENAI].model).toBe('gpt-4');
      expect(config.modelProviders[LLMProvider.OPENAI].apiKey).toBe('test-key');
    });

    it('should use environment variables for API keys', () => {
      process.env.OPENAI_API_KEY = 'env-openai-key';
      
      const config = loadConfig({
        provider: LLMProvider.OPENAI,
      });
      
      expect(config.modelProviders[LLMProvider.OPENAI].apiKey).toBe('env-openai-key');
    });

    it('should set custom max steps', () => {
      const config = loadConfig({
        maxSteps: 50,
      });
      
      expect(config.maxSteps).toBe(50);
    });
  });
});
