
function greet(name) {
    return `Hello, ${name}!`;
}

class Person {
    constructor(name, age) {
        this.name = name;
        this.age = age;
    }
    
    introduce() {
        return `Hi, I'm ${this.name} and I'm ${this.age} years old.`;
    }
    
    celebrateBirthday() {
        this.age++;
        return `Happy birthday! Now I'm ${this.age}.`;
    }
}

module.exports = { greet, Person };
