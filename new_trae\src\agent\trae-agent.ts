/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import * as fs from 'fs';
import * as path from 'path';
import { spawn } from 'child_process';
import { Agent } from './base';
import { LLMMessage, LLMResponse } from '../types/llm';
import { AgentExecution, AgentState, AgentStep } from '../types/agent';
import { Config } from '../types/config';
import { LLMClient } from '../utils/llm-client';
import { TrajectoryRecorder } from '../utils/trajectory-recorder';
import { CLIConsole } from '../utils/cli-console';
import { createTools, TRAE_AGENT_TOOL_NAMES } from '../tools';
import { ToolExecutor } from '../tools/base';

/**
 * System prompt for Trae Agent
 */
const TRAE_AGENT_SYSTEM_PROMPT = `You are <PERSON>rae <PERSON>, an AI assistant specialized in software engineering tasks. You have access to various tools to help you complete programming and development tasks.

Available tools:
- str_replace_based_edit_tool: For viewing, creating, and editing files
- bash: For running shell commands
- json_edit_tool: For editing JSON files
- sequentialthinking: For breaking down complex problems
- task_done: For marking tasks as complete

Guidelines:
1. Always understand the task thoroughly before starting
2. Use the sequentialthinking tool for complex problems
3. Test your solutions when possible
4. Use appropriate tools for each task
5. Be systematic and methodical in your approach
6. Call task_done only when the task is truly complete and verified

Remember to be helpful, accurate, and thorough in your work.`;

/**
 * Trae Agent specialized for software engineering tasks
 */
export class TraeAgent extends Agent {
  private projectPath = '';
  private baseCommit?: string;
  private mustPatch = false;
  private patchPath?: string;

  constructor(config?: Config, llmClient?: LLMClient) {
    super(config, llmClient);
  }

  /**
   * Create TraeAgent from config
   */
  static fromConfig(config: Config): TraeAgent {
    return new TraeAgent(config);
  }

  /**
   * Set up trajectory recording for this agent
   */
  setupTrajectoryRecording(trajectoryPath?: string): string {
    const recorder = new TrajectoryRecorder(trajectoryPath);
    this.setTrajectoryRecorder(recorder);
    return recorder.getTrajectoryPath();
  }

  /**
   * Create a new task
   */
  newTask(
    task: string,
    extraArgs?: Record<string, string>,
    toolNames?: string[]
  ): void {
    this.task = task;

    // Process extra arguments
    if (extraArgs) {
      this.projectPath = extraArgs.project_path || process.cwd();
      this.baseCommit = extraArgs.base_commit;
      this.mustPatch = extraArgs.must_patch === 'true';
      this.patchPath = extraArgs.patch_path;
    }

    // Set up tools
    const toolNamesToUse = toolNames || TRAE_AGENT_TOOL_NAMES;
    this.tools = createTools(toolNamesToUse, this.modelParameters.model);
    this.toolCaller = new ToolExecutor(this.tools);

    // Set up initial messages
    this.initialMessages = [
      {
        role: 'system',
        content: TRAE_AGENT_SYSTEM_PROMPT,
      },
      {
        role: 'user',
        content: this.formatTaskMessage(task, extraArgs),
      },
    ];

    // Start trajectory recording if recorder is available
    if (this.trajectoryRecorder) {
      this.trajectoryRecorder.startRecording(
        task,
        'trae-agent',
        this.modelParameters.model,
        this.maxSteps
      );
    }
  }

  /**
   * Execute the task and finalize trajectory recording
   */
  async executeTask(): Promise<AgentExecution> {
    const execution = await super.executeTask();

    // Finalize trajectory recording if recorder is available
    if (this.trajectoryRecorder) {
      this.trajectoryRecorder.finalizeRecording(
        execution.success,
        execution.finalResult
      );
    }

    // Generate patch if required
    if (this.patchPath) {
      try {
        const patch = this.getGitDiff();
        fs.writeFileSync(this.patchPath, patch);
      } catch (error) {
        console.warn('Failed to generate patch:', error);
      }
    }

    return execution;
  }

  /**
   * Check if the LLM indicates that the task is completed
   */
  llmIndicatesTaskCompleted(llmResponse: LLMResponse): boolean {
    if (!llmResponse.toolCalls) {
      return false;
    }
    return llmResponse.toolCalls.some(toolCall => toolCall.name === 'task_done');
  }

  /**
   * Enhanced task completion detection
   */
  protected isTaskCompleted(llmResponse: LLMResponse): boolean {
    if (this.mustPatch) {
      const patch = this.getGitDiff();
      const cleanPatch = this.removePatchesToTests(patch);
      if (!cleanPatch.trim()) {
        return false;
      }
    }

    return true;
  }

  /**
   * Return a message indicating that the task is incomplete
   */
  protected taskIncompleteMessage(): string {
    return 'ERROR! Your Patch is empty. Please provide a patch that fixes the problem.';
  }

  /**
   * Format the task message with context
   */
  private formatTaskMessage(task: string, extraArgs?: Record<string, string>): string {
    let message = `Task: ${task}\n\n`;

    if (extraArgs?.project_path) {
      message += `Working directory: ${extraArgs.project_path}\n`;
    }

    if (this.mustPatch) {
      message += `Note: This task requires generating a patch. Make sure to make meaningful changes to the codebase.\n`;
    }

    message += `\nPlease complete this task step by step. Use the available tools to understand the codebase, make necessary changes, and verify your solution.`;

    return message;
  }

  /**
   * Get git diff for the current changes
   */
  private getGitDiff(): string {
    try {
      const result = spawn('git', ['diff'], {
        cwd: this.projectPath,
        stdio: 'pipe',
      });

      let output = '';
      result.stdout?.on('data', (data) => {
        output += data.toString();
      });

      // Wait for process to complete (simplified synchronous approach)
      return output;
    } catch (error) {
      console.warn('Failed to get git diff:', error);
      return '';
    }
  }

  /**
   * Remove patches to test files
   */
  private removePatchesToTests(patch: string): string {
    const lines = patch.split('\n');
    const filteredLines: string[] = [];
    let inTestFile = false;

    for (const line of lines) {
      if (line.startsWith('diff --git')) {
        // Check if this is a test file
        inTestFile = this.isTestFile(line);
      }

      if (!inTestFile) {
        filteredLines.push(line);
      }
    }

    return filteredLines.join('\n');
  }

  /**
   * Check if a file path indicates a test file
   */
  private isTestFile(diffLine: string): boolean {
    const testPatterns = [
      /test_/,
      /_test\./,
      /\.test\./,
      /\.spec\./,
      /\/tests?\//,
      /\/spec\//,
      /__tests__\//,
    ];

    return testPatterns.some(pattern => pattern.test(diffLine));
  }

  /**
   * Get project information
   */
  getProjectInfo(): {
    projectPath: string;
    baseCommit?: string;
    mustPatch: boolean;
    patchPath?: string;
  } {
    return {
      projectPath: this.projectPath,
      baseCommit: this.baseCommit,
      mustPatch: this.mustPatch,
      patchPath: this.patchPath,
    };
  }
}
