/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { Config } from '../types/config';
import { AgentExecution, AgentState, AgentStep } from '../types/agent';
import { LakeView } from './lake-view';

/**
 * Console step for display
 */
interface ConsoleStep {
  stepNumber: number;
  state: AgentState;
  summary: string;
  details: string;
  timestamp: string;
  lakeViewSummary?: string;
}

/**
 * CLI Console for displaying agent progress
 */
export class CLIConsole {
  private config: Config;
  private consoleSteps: Map<number, ConsoleStep> = new Map();
  private lakeView?: LakeView;
  private agentStepHistory: AgentStep[] = [];
  private agentExecution?: AgentExecution;
  private isDisplaying = false;

  constructor(config: Config) {
    this.config = config;
    if (config.enableLakeview && config.lakeviewConfig) {
      this.lakeView = new LakeView(config);
    }
  }

  /**
   * Print task details at the start
   */
  printTaskDetails(
    task: string,
    workingDir: string,
    provider: string,
    model: string,
    maxSteps: number,
    configFile: string,
    trajectoryPath: string
  ): void {
    console.log('\n🎯 Trae Agent Task Execution');
    console.log('=' .repeat(50));
    console.log(`📋 Task: ${task}`);
    console.log(`📁 Working Directory: ${workingDir}`);
    console.log(`🤖 Provider: ${provider}`);
    console.log(`🧠 Model: ${model}`);
    console.log(`🔢 Max Steps: ${maxSteps}`);
    console.log(`⚙️  Config File: ${configFile}`);
    console.log(`📊 Trajectory: ${trajectoryPath}`);
    console.log('=' .repeat(50));
    console.log();
  }

  /**
   * Start displaying progress
   */
  startProgressDisplay(): void {
    if (this.isDisplaying) return;
    this.isDisplaying = true;
    
    // Start a simple progress display loop
    this.displayProgress();
  }

  /**
   * Stop displaying progress
   */
  stopProgressDisplay(): void {
    this.isDisplaying = false;
  }

  /**
   * Add an agent step to the history
   */
  addAgentStep(step: AgentStep): void {
    this.agentStepHistory.push(step);
    this.updateConsoleStep(step);
    this.displayProgress();
  }

  /**
   * Set the final execution result
   */
  setAgentExecution(execution: AgentExecution): void {
    this.agentExecution = execution;
    this.displayFinalResult();
  }

  /**
   * Update console step information
   */
  private updateConsoleStep(step: AgentStep): void {
    const consoleStep: ConsoleStep = {
      stepNumber: step.stepNumber,
      state: step.state,
      summary: this.createStepSummary(step),
      details: this.createStepDetails(step),
      timestamp: step.timestamp || new Date().toISOString(),
    };

    // Add Lake View summary if available
    if (this.lakeView) {
      this.lakeView.createLakeviewStep(step).then(lakeViewStep => {
        if (lakeViewStep) {
          consoleStep.lakeViewSummary = `${lakeViewStep.tagsEmoji} ${lakeViewStep.descTask}`;
        }
      });
    }

    this.consoleSteps.set(step.stepNumber, consoleStep);
  }

  /**
   * Create step summary
   */
  private createStepSummary(step: AgentStep): string {
    const stateEmoji = this.getStateEmoji(step.state);
    let summary = `${stateEmoji} Step ${step.stepNumber}: ${step.state}`;
    
    if (step.toolCalls && step.toolCalls.length > 0) {
      const toolNames = step.toolCalls.map(call => call.name).join(', ');
      summary += ` (Tools: ${toolNames})`;
    }
    
    return summary;
  }

  /**
   * Create step details
   */
  private createStepDetails(step: AgentStep): string {
    let details = '';
    
    if (step.llmResponse?.content) {
      const content = step.llmResponse.content.substring(0, 200);
      details += `💭 LLM Response: ${content}${content.length >= 200 ? '...' : ''}\n`;
    }
    
    if (step.toolResults && step.toolResults.length > 0) {
      details += '🔧 Tool Results:\n';
      for (const result of step.toolResults) {
        const output = result.result.output?.substring(0, 100) || 'No output';
        details += `  - ${result.name}: ${output}${output.length >= 100 ? '...' : ''}\n`;
      }
    }
    
    if (step.error) {
      details += `❌ Error: ${step.error}\n`;
    }
    
    return details;
  }

  /**
   * Get emoji for agent state
   */
  private getStateEmoji(state: AgentState): string {
    switch (state) {
      case 'thinking':
        return '🤔';
      case 'tool_calling':
        return '🔧';
      case 'completed':
        return '✅';
      case 'failed':
        return '❌';
      case 'max_steps_reached':
        return '⏰';
      default:
        return '📝';
    }
  }

  /**
   * Display current progress
   */
  private displayProgress(): void {
    if (!this.isDisplaying) return;

    // Clear previous output (simple approach)
    console.clear();
    
    // Re-display task header
    console.log('\n🎯 Trae Agent - Task Execution Progress');
    console.log('=' .repeat(50));
    
    // Display steps
    const sortedSteps = Array.from(this.consoleSteps.values())
      .sort((a, b) => a.stepNumber - b.stepNumber);
    
    for (const step of sortedSteps) {
      console.log(`\n${step.summary}`);
      console.log(`⏰ ${new Date(step.timestamp).toLocaleTimeString()}`);
      
      if (step.lakeViewSummary) {
        console.log(`🌊 ${step.lakeViewSummary}`);
      }
      
      if (step.details) {
        console.log(step.details);
      }
      
      console.log('-'.repeat(40));
    }
    
    // Show current status
    if (this.agentStepHistory.length > 0) {
      const lastStep = this.agentStepHistory[this.agentStepHistory.length - 1];
      console.log(`\n📊 Current Status: ${this.getStateEmoji(lastStep.state)} ${lastStep.state}`);
      console.log(`📈 Progress: ${this.agentStepHistory.length} steps completed`);
    }
  }

  /**
   * Display final execution result
   */
  private displayFinalResult(): void {
    console.clear();
    
    console.log('\n🎯 Trae Agent - Task Execution Complete');
    console.log('=' .repeat(50));
    
    if (this.agentExecution) {
      const statusEmoji = this.agentExecution.success ? '✅' : '❌';
      const statusText = this.agentExecution.success ? 'SUCCESS' : 'FAILED';
      
      console.log(`\n${statusEmoji} Final Status: ${statusText}`);
      console.log(`📊 Total Steps: ${this.agentExecution.steps.length}`);
      console.log(`⏱️  Execution Time: ${this.agentExecution.executionTime}ms`);
      
      if (this.agentExecution.finalResult) {
        console.log(`\n📝 Final Result:`);
        console.log(this.agentExecution.finalResult);
      }
      
      if (this.agentExecution.error) {
        console.log(`\n❌ Error: ${this.agentExecution.error}`);
      }
    }
    
    // Display Lake View summary if available
    if (this.lakeView && this.agentStepHistory.length > 0) {
      const summary = this.lakeView.createExecutionSummary(this.agentStepHistory);
      console.log('\n🌊 Lake View Summary:');
      console.log(summary);
    }
    
    console.log('\n' + '=' .repeat(50));
  }
}
