/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { CLIConsole } from '../cli-console';
import { Config } from '../../types/config';
import { AgentStep, AgentExecution } from '../../types/agent';

// Mock console methods
const originalConsoleLog = console.log;
const originalConsoleClear = console.clear;

describe('CLIConsole', () => {
  let cliConsole: CLIConsole;
  let mockConfig: Config;
  let consoleOutput: string[];

  beforeEach(() => {
    consoleOutput = [];
    
    // Mock console methods
    console.log = jest.fn((...args: any[]) => {
      consoleOutput.push(args.join(' '));
    });
    
    console.clear = jest.fn();

    mockConfig = {
      defaultProvider: 'openai',
      modelProviders: {
        openai: {
          model: 'gpt-4o',
          baseUrl: 'https://api.openai.com/v1',
          apiKey: 'test-key',
          maxTokens: 4000,
          temperature: 0.7,
          topP: 1.0,
          topK: 0,
          parallelToolCalls: true,
          maxRetries: 3,
        },
      },
      maxSteps: 20,
      enableLakeview: true,
      lakeviewConfig: {
        modelProvider: 'openai',
        modelName: 'gpt-4o',
        enabled: true,
      },
    };

    cliConsole = new CLIConsole(mockConfig);
  });

  afterEach(() => {
    console.log = originalConsoleLog;
    console.clear = originalConsoleClear;
  });

  describe('printTaskDetails', () => {
    it('should print task details correctly', () => {
      cliConsole.printTaskDetails(
        'Test task',
        '/test/dir',
        'openai',
        'gpt-4o',
        20,
        'config.json',
        'trajectory.json'
      );

      expect(consoleOutput.join('\n')).toContain('Trae Agent Task Execution');
      expect(consoleOutput.join('\n')).toContain('Task: Test task');
      expect(consoleOutput.join('\n')).toContain('Working Directory: /test/dir');
      expect(consoleOutput.join('\n')).toContain('Provider: openai');
      expect(consoleOutput.join('\n')).toContain('Model: gpt-4o');
      expect(consoleOutput.join('\n')).toContain('Max Steps: 20');
      expect(consoleOutput.join('\n')).toContain('Config File: config.json');
      expect(consoleOutput.join('\n')).toContain('Trajectory: trajectory.json');
    });
  });

  describe('startProgressDisplay', () => {
    it('should start progress display', () => {
      expect(() => cliConsole.startProgressDisplay()).not.toThrow();
    });

    it('should not start multiple displays', () => {
      cliConsole.startProgressDisplay();
      expect(() => cliConsole.startProgressDisplay()).not.toThrow();
    });
  });

  describe('stopProgressDisplay', () => {
    it('should stop progress display', () => {
      cliConsole.startProgressDisplay();
      expect(() => cliConsole.stopProgressDisplay()).not.toThrow();
    });
  });

  describe('addAgentStep', () => {
    it('should add agent step and display progress', () => {
      const step: AgentStep = {
        stepNumber: 1,
        state: 'thinking',
        timestamp: new Date().toISOString(),
        llmResponse: {
          content: 'Test response',
          role: 'assistant',
        },
      };

      cliConsole.startProgressDisplay();
      cliConsole.addAgentStep(step);

      expect(console.clear).toHaveBeenCalled();
      expect(consoleOutput.join('\n')).toContain('Step 1: thinking');
    });

    it('should handle step with tool calls', () => {
      const step: AgentStep = {
        stepNumber: 2,
        state: 'tool_calling',
        timestamp: new Date().toISOString(),
        toolCalls: [
          {
            id: 'test-1',
            name: 'bash',
            arguments: { command: 'ls' },
          },
        ],
        toolResults: [
          {
            id: 'test-1',
            name: 'bash',
            result: {
              output: 'file1.txt\nfile2.txt',
            },
          },
        ],
      };

      cliConsole.startProgressDisplay();
      cliConsole.addAgentStep(step);

      expect(consoleOutput.join('\n')).toContain('Tools: bash');
      expect(consoleOutput.join('\n')).toContain('Tool Results:');
    });

    it('should handle step with error', () => {
      const step: AgentStep = {
        stepNumber: 3,
        state: 'failed',
        timestamp: new Date().toISOString(),
        error: 'Test error message',
      };

      cliConsole.startProgressDisplay();
      cliConsole.addAgentStep(step);

      expect(consoleOutput.join('\n')).toContain('Error: Test error message');
    });
  });

  describe('setAgentExecution', () => {
    it('should display successful execution result', () => {
      const execution: AgentExecution = {
        task: 'Test task',
        steps: [],
        success: true,
        executionTime: 1500,
        finalResult: 'Task completed successfully',
      };

      cliConsole.setAgentExecution(execution);

      expect(console.clear).toHaveBeenCalled();
      expect(consoleOutput.join('\n')).toContain('Task Execution Complete');
      expect(consoleOutput.join('\n')).toContain('Final Status: SUCCESS');
      expect(consoleOutput.join('\n')).toContain('Execution Time: 1500ms');
      expect(consoleOutput.join('\n')).toContain('Final Result:');
      expect(consoleOutput.join('\n')).toContain('Task completed successfully');
    });

    it('should display failed execution result', () => {
      const execution: AgentExecution = {
        task: 'Test task',
        steps: [],
        success: false,
        executionTime: 800,
        error: 'Task failed due to error',
      };

      cliConsole.setAgentExecution(execution);

      expect(consoleOutput.join('\n')).toContain('Final Status: FAILED');
      expect(consoleOutput.join('\n')).toContain('Error: Task failed due to error');
    });
  });

  describe('state emoji mapping', () => {
    it('should return correct emojis for different states', () => {
      const states = [
        { state: 'thinking', emoji: '🤔' },
        { state: 'tool_calling', emoji: '🔧' },
        { state: 'completed', emoji: '✅' },
        { state: 'failed', emoji: '❌' },
        { state: 'max_steps_reached', emoji: '⏰' },
      ];

      for (const { state, emoji } of states) {
        const step: AgentStep = {
          stepNumber: 1,
          state: state as any,
          timestamp: new Date().toISOString(),
        };

        cliConsole.startProgressDisplay();
        cliConsole.addAgentStep(step);

        expect(consoleOutput.join('\n')).toContain(emoji);
        consoleOutput.length = 0; // Clear output for next test
      }
    });
  });

  describe('Lake View integration', () => {
    it('should work without Lake View when disabled', () => {
      const configWithoutLakeView = {
        ...mockConfig,
        enableLakeview: false,
        lakeviewConfig: undefined,
      };

      const consoleWithoutLakeView = new CLIConsole(configWithoutLakeView);
      
      const step: AgentStep = {
        stepNumber: 1,
        state: 'thinking',
        timestamp: new Date().toISOString(),
      };

      expect(() => {
        consoleWithoutLakeView.startProgressDisplay();
        consoleWithoutLakeView.addAgentStep(step);
      }).not.toThrow();
    });
  });
});
