/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

/**
 * Basic usage example of Trae Agent TypeScript implementation
 */

import { TraeAgent } from '../src/agent/trae-agent';
import { loadConfig } from '../src/utils/config';
import { LLMProvider } from '../src/types/llm';

async function basicExample() {
  try {
    // Load configuration
    const config = loadConfig({
      provider: LLMProvider.ANTHROPIC,
      model: 'claude-sonnet-4-20250514',
      apiKey: process.env.ANTHROPIC_API_KEY || 'your-api-key-here',
      maxSteps: 10,
    });

    // Create agent
    const agent = TraeAgent.fromConfig(config);

    // Set up trajectory recording
    const trajectoryPath = agent.setupTrajectoryRecording();
    console.log(`📊 Trajectory will be saved to: ${trajectoryPath}`);

    // Create a simple task
    const task = 'Create a simple TypeScript function that calculates the factorial of a number';
    
    console.log(`🚀 Starting task: ${task}`);

    // Execute the task
    agent.newTask(task, {
      project_path: process.cwd(),
    });

    const execution = await agent.executeTask();

    // Display results
    console.log('\n' + '='.repeat(50));
    if (execution.success) {
      console.log('✅ Task completed successfully!');
    } else {
      console.log('❌ Task failed.');
    }

    if (execution.finalResult) {
      console.log(`📝 Result: ${execution.finalResult}`);
    }

    console.log(`⏱️  Execution time: ${execution.executionTime.toFixed(2)}s`);
    console.log(`🔢 Steps taken: ${execution.steps.length}`);

    if (execution.totalTokens) {
      console.log(`🎯 Tokens used: ${execution.totalTokens.totalTokens}`);
    }

    // Show step details
    console.log('\n📋 Execution Steps:');
    for (const step of execution.steps) {
      console.log(`  Step ${step.stepNumber}: ${step.state}`);
      if (step.toolCalls) {
        console.log(`    Tools used: ${step.toolCalls.map(tc => tc.name).join(', ')}`);
      }
    }

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Run the example if this file is executed directly
if (require.main === module) {
  basicExample().catch(console.error);
}

export { basicExample };
