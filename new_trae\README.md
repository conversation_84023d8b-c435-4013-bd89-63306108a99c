# Trae Agent (TypeScript)

[![TypeScript](https://img.shields.io/badge/TypeScript-5.4+-blue.svg)](https://www.typescriptlang.org/) [![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT) ![Alpha](https://img.shields.io/badge/Status-Alpha-red)

**Trae Agent (TypeScript)** is a TypeScript implementation of the LLM-based agent for general purpose software engineering tasks. It provides a powerful CLI interface that can understand natural language instructions and execute complex software engineering workflows using various tools and LLM providers.

This is a complete TypeScript rewrite of the original Python [Trae Agent](https://github.com/bytedance/trae-agent), offering the same functionality with improved type safety and modern JavaScript ecosystem integration.

## ✨ Features

- 🌊 **Lakeview**: Provides short and concise summarization for agent steps
- 🤖 **Multi-LLM Support**: Works with OpenAI, Anthropic, Google Gemini, Azure, OpenRouter, Ollama and Doubao APIs
- 🛠️ **Rich Tool Ecosystem**: File editing, bash execution, sequential thinking, JSON editing, and more
- 🎯 **Interactive Mode**: Conversational interface for iterative development
- 📊 **Trajectory Recording**: Detailed logging of all agent actions for debugging and analysis
- ⚙️ **Flexible Configuration**: JSON-based configuration with environment variable support
- 🚀 **Easy Installation**: Simple npm-based installation
- 🔒 **Type Safety**: Full TypeScript support with comprehensive type definitions

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/bytedance/trae-agent.git
cd trae-agent/new_trae

# Install dependencies
npm install

# Build the project
npm run build

# Install globally (optional)
npm install -g .
```

### Setup API Keys

Set your API keys as environment variables:

```bash
# For OpenAI
export OPENAI_API_KEY="your-openai-api-key"

# For Anthropic
export ANTHROPIC_API_KEY="your-anthropic-api-key"

# For Google Gemini
export GOOGLE_API_KEY="your-google-api-key"

# For OpenRouter
export OPENROUTER_API_KEY="your-openrouter-api-key"

# For Doubao
export DOUBAO_API_KEY="your-doubao-api-key"
export DOUBAO_BASE_URL="your-doubao-base-url"

# Optional: For OpenRouter rankings
export OPENROUTER_SITE_URL="https://your-site.com"
export OPENROUTER_SITE_NAME="Your App Name"
```

### Basic Usage

```bash
# Run a simple task
npx trae-cli run "Create a hello world TypeScript script"

# Run with specific provider and model
npx trae-cli run "Create a hello world script" --provider openai --model gpt-4o

# Run with Google Gemini
npx trae-cli run "Create a data parsing function" --provider google --model gemini-2.5-pro

# Interactive mode
npx trae-cli interactive

# Show configuration
npx trae-cli show-config

# List available tools
npx trae-cli tools
```

## 📖 Usage

### Command Line Interface

#### `trae-cli run` - Execute a Task

```bash
# Basic task execution
trae-cli run "Create a TypeScript script that calculates fibonacci numbers"

# With specific provider and model
trae-cli run "Fix the bug in main.ts" --provider anthropic --model claude-sonnet-4-20250514

# With custom working directory
trae-cli run "Add unit tests for the utils module" --working-dir /path/to/project

# Save trajectory for debugging
trae-cli run "Refactor the database module" --trajectory-file debug_session.json

# Force to generate patches
trae-cli run "Update the API endpoints" --must-patch
```

#### `trae-cli interactive` - Interactive Mode

```bash
# Start interactive session
trae-cli interactive

# With custom configuration
trae-cli interactive --provider openai --model gpt-4o --max-steps 30
```

In interactive mode, you can:
- Type any task description to execute it
- Use `status` to see agent information
- Use `help` for available commands
- Use `clear` to clear the screen
- Use `exit` or `quit` to end the session

### Configuration

Create a `trae_config.json` file in your project root:

```json
{
  "default_provider": "anthropic",
  "max_steps": 20,
  "enable_lakeview": true,
  "model_providers": {
    "openai": {
      "api_key": "your_openai_api_key",
      "base_url": "https://api.openai.com/v1",
      "model": "gpt-4o",
      "max_tokens": 128000,
      "temperature": 0.5,
      "top_p": 1,
      "max_retries": 10
    },
    "anthropic": {
      "api_key": "your_anthropic_api_key",
      "base_url": "https://api.anthropic.com",
      "model": "claude-sonnet-4-20250514",
      "max_tokens": 4096,
      "temperature": 0.5,
      "top_p": 1,
      "top_k": 0,
      "max_retries": 10
    }
  }
}
```

**Configuration Priority:**
1. Command-line arguments (highest)
2. Configuration file values
3. Environment variables
4. Default values (lowest)

## 🛠️ Available Tools

- **str_replace_based_edit_tool**: File and directory manipulation with persistent state
- **bash**: Execute shell commands in a persistent session
- **json_edit_tool**: Edit JSON files using JSONPath expressions
- **sequentialthinking**: Break down complex problems through structured thinking
- **task_done**: Mark tasks as complete

## 📊 Trajectory Recording

Trae Agent automatically records detailed execution trajectories:

```bash
# Auto-generated trajectory file
trae-cli run "Debug the authentication module"
# Saves to: trajectories/trajectory_20250612_220546.json

# Custom trajectory file
trae-cli run "Optimize the database queries" --trajectory-file optimization_debug.json
```

## 🧪 Development

```bash
# Install dependencies
npm install

# Run in development mode
npm run dev

# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Lint code
npm run lint

# Format code
npm run format

# Build for production
npm run build
```

## 📋 Requirements

- Node.js 18.0.0+
- TypeScript 5.4+
- API key for your chosen provider

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new functionality
5. Run tests and linting (`npm test && npm run lint`)
6. Commit your changes (`git commit -m 'Add amazing feature'`)
7. Push to the branch (`git push origin feature/amazing-feature`)
8. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

This TypeScript implementation is based on the original Python [Trae Agent](https://github.com/bytedance/trae-agent) project by ByteDance.
