/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { TaskDoneTool } from '../../tools/task-done-tool';

describe('TaskDoneTool', () => {
  let tool: TaskDoneTool;

  beforeEach(() => {
    tool = new TaskDoneTool();
  });

  describe('basic properties', () => {
    it('should have correct name', () => {
      expect(tool.getName()).toBe('task_done');
    });

    it('should have description', () => {
      const description = tool.getDescription();
      expect(description).toContain('completion');
      expect(description).toContain('task');
    });

    it('should have no parameters', () => {
      const parameters = tool.getParameters();
      expect(parameters).toHaveLength(0);
    });
  });

  describe('execute', () => {
    it('should return success message', async () => {
      const result = await tool.execute({});
      
      expect(result.output).toBe('Task done.');
      expect(result.error).toBeUndefined();
      expect(result.errorCode).toBeUndefined();
    });

    it('should work with any arguments', async () => {
      const result = await tool.execute({ 
        someArg: 'value',
        anotherArg: 123,
      });
      
      expect(result.output).toBe('Task done.');
    });
  });

  describe('input schema', () => {
    it('should generate correct input schema', () => {
      const schema = tool.getInputSchema();
      
      expect(schema.type).toBe('object');
      expect(schema.properties).toEqual({});
      expect(schema.required).toEqual([]);
      expect(schema.additionalProperties).toBe(false);
    });
  });
});
