/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import * as fs from 'fs';
import * as path from 'path';
import { CKGTool } from '../ckg-tool';

describe('CKGTool', () => {
  let ckgTool: CKGTool;
  let testDir: string;

  beforeEach(() => {
    ckgTool = new CKGTool();
    testDir = path.join(__dirname, 'test-codebase');
    
    // Create test directory
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
    
    // Create test files
    const pythonCode = `
def hello_world():
    """A simple hello world function"""
    print("Hello, World!")
    return "Hello, World!"

class Calculator:
    """A simple calculator class"""
    
    def __init__(self):
        self.result = 0
    
    def add(self, x, y):
        """Add two numbers"""
        self.result = x + y
        return self.result
    
    def multiply(self, x, y):
        """Multiply two numbers"""
        self.result = x * y
        return self.result
`;
    
    const jsCode = `
function greet(name) {
    return \`Hello, \${name}!\`;
}

class Person {
    constructor(name, age) {
        this.name = name;
        this.age = age;
    }
    
    introduce() {
        return \`Hi, I'm \${this.name} and I'm \${this.age} years old.\`;
    }
    
    celebrateBirthday() {
        this.age++;
        return \`Happy birthday! Now I'm \${this.age}.\`;
    }
}
`;
    
    fs.writeFileSync(path.join(testDir, 'example.py'), pythonCode);
    fs.writeFileSync(path.join(testDir, 'example.js'), jsCode);
  });

  afterEach(() => {
    // Clean up test directory
    if (fs.existsSync(testDir)) {
      fs.rmSync(testDir, { recursive: true, force: true });
    }
    
    // Clean up CKG database files
    const ckgDir = path.join(process.cwd(), '.trae', 'ckg');
    if (fs.existsSync(ckgDir)) {
      fs.rmSync(ckgDir, { recursive: true, force: true });
    }
  });

  describe('getName', () => {
    it('should return correct tool name', () => {
      expect(ckgTool.getName()).toBe('ckg');
    });
  });

  describe('getDescription', () => {
    it('should return tool description', () => {
      const description = ckgTool.getDescription();
      expect(description).toContain('code knowledge graph');
      expect(description).toContain('search_function');
      expect(description).toContain('search_class');
    });
  });

  describe('getParameters', () => {
    it('should return correct parameters', () => {
      const params = ckgTool.getParameters();
      expect(params).toHaveLength(4);
      
      const commandParam = params.find(p => p.name === 'command');
      expect(commandParam).toBeDefined();
      expect(commandParam?.required).toBe(true);
      expect(commandParam?.enum).toContain('search_function');
      
      const pathParam = params.find(p => p.name === 'path');
      expect(pathParam).toBeDefined();
      expect(pathParam?.required).toBe(true);
      
      const identifierParam = params.find(p => p.name === 'identifier');
      expect(identifierParam).toBeDefined();
      expect(identifierParam?.required).toBe(true);
      
      const printBodyParam = params.find(p => p.name === 'print_body');
      expect(printBodyParam).toBeDefined();
      expect(printBodyParam?.required).toBe(false);
    });
  });

  describe('execute', () => {
    it('should return error for missing command', async () => {
      const result = await ckgTool.execute({
        path: testDir,
        identifier: 'test',
      });
      
      expect(result.error).toContain('No command provided');
      expect(result.errorCode).toBe(-1);
    });

    it('should return error for missing path', async () => {
      const result = await ckgTool.execute({
        command: 'search_function',
        identifier: 'test',
      });
      
      expect(result.error).toContain('No path provided');
      expect(result.errorCode).toBe(-1);
    });

    it('should return error for missing identifier', async () => {
      const result = await ckgTool.execute({
        command: 'search_function',
        path: testDir,
      });
      
      expect(result.error).toContain('No identifier provided');
      expect(result.errorCode).toBe(-1);
    });

    it('should return error for non-existent path', async () => {
      const result = await ckgTool.execute({
        command: 'search_function',
        path: '/non/existent/path',
        identifier: 'test',
      });
      
      expect(result.error).toContain('does not exist');
      expect(result.errorCode).toBe(-1);
    });

    it('should return error for file path instead of directory', async () => {
      const filePath = path.join(testDir, 'example.py');
      const result = await ckgTool.execute({
        command: 'search_function',
        path: filePath,
        identifier: 'test',
      });
      
      expect(result.error).toContain('is not a directory');
      expect(result.errorCode).toBe(-1);
    });

    it('should search for functions successfully', async () => {
      const result = await ckgTool.execute({
        command: 'search_function',
        path: testDir,
        identifier: 'hello',
      });
      
      expect(result.error).toBeUndefined();
      expect(result.output).toContain('hello_world');
      expect(result.output).toContain('Function:');
    });

    it('should search for classes successfully', async () => {
      const result = await ckgTool.execute({
        command: 'search_class',
        path: testDir,
        identifier: 'Calculator',
      });
      
      expect(result.error).toBeUndefined();
      expect(result.output).toContain('Calculator');
      expect(result.output).toContain('Class:');
    });

    it('should handle print_body parameter', async () => {
      const resultWithBody = await ckgTool.execute({
        command: 'search_function',
        path: testDir,
        identifier: 'hello',
        print_body: true,
      });
      
      expect(resultWithBody.output).toContain('Body:');
      
      const resultWithoutBody = await ckgTool.execute({
        command: 'search_function',
        path: testDir,
        identifier: 'hello',
        print_body: false,
      });
      
      expect(resultWithoutBody.output).not.toContain('Body:');
    });

    it('should return appropriate message for no matches', async () => {
      const result = await ckgTool.execute({
        command: 'search_function',
        path: testDir,
        identifier: 'nonexistent',
      });
      
      expect(result.error).toBeUndefined();
      expect(result.output).toContain('No functions found matching');
    });

    it('should return error for invalid command', async () => {
      const result = await ckgTool.execute({
        command: 'invalid_command' as any,
        path: testDir,
        identifier: 'test',
      });
      
      expect(result.error).toContain('Invalid command');
      expect(result.errorCode).toBe(-1);
    });
  });
});
