#!/usr/bin/env node

/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { Command } from 'commander';
import * as dotenv from 'dotenv';
import * as path from 'path';
import { TraeAgent } from './agent/trae-agent';
import { loadConfig } from './utils/config';
import { LLMProvider } from './types/llm';
import { toolsRegistry } from './tools';

// Load environment variables
dotenv.config();

const program = new Command();

/**
 * Create agent with configuration
 */
function createAgent(configPath: string, options: any): TraeAgent {
  try {
    const config = loadConfig({
      configFile: configPath,
      provider: options.provider,
      model: options.model,
      modelBaseUrl: options.modelBaseUrl,
      apiKey: options.apiKey,
      maxSteps: options.maxSteps,
    });

    return TraeAgent.fromConfig(config);
  } catch (error) {
    console.error('Error creating agent:', error);
    process.exit(1);
  }
}

/**
 * Run command
 */
program
  .command('run')
  .description('Execute a task')
  .argument('<task>', 'Task description')
  .option('-c, --config-file <path>', 'Path to configuration file', 'trae_config.json')
  .option('-p, --provider <provider>', 'LLM provider')
  .option('-m, --model <model>', 'Model name')
  .option('--model-base-url <url>', 'Model base URL')
  .option('--api-key <key>', 'API key')
  .option('--max-steps <number>', 'Maximum number of steps', parseInt)
  .option('-w, --working-dir <path>', 'Working directory', process.cwd())
  .option('-t, --trajectory-file <path>', 'Trajectory file path')
  .option('--must-patch', 'Force to generate patches')
  .option('--patch-path <path>', 'Path to save patch file')
  .action(async (task: string, options) => {
    try {
      console.log(`🚀 Starting Trae Agent...`);
      console.log(`📋 Task: ${task}`);
      console.log(`📁 Working directory: ${options.workingDir}`);

      // Create agent
      const agent = createAgent(options.configFile, options);

      // Set up trajectory recording
      let trajectoryPath: string;
      if (options.trajectoryFile) {
        trajectoryPath = agent.setupTrajectoryRecording(options.trajectoryFile);
      } else {
        trajectoryPath = agent.setupTrajectoryRecording();
      }

      console.log(`📊 Trajectory will be saved to: ${trajectoryPath}`);

      // Prepare task arguments
      const taskArgs = {
        project_path: options.workingDir,
        must_patch: options.mustPatch ? 'true' : 'false',
        patch_path: options.patchPath,
      };

      // Execute task
      agent.newTask(task, taskArgs);
      const execution = await agent.executeTask();

      // Display results
      console.log('\n' + '='.repeat(50));
      if (execution.success) {
        console.log('✅ Task completed successfully!');
      } else {
        console.log('❌ Task failed.');
      }

      if (execution.finalResult) {
        console.log(`📝 Result: ${execution.finalResult}`);
      }

      console.log(`⏱️  Execution time: ${execution.executionTime.toFixed(2)}s`);
      console.log(`🔢 Steps taken: ${execution.steps.length}`);

      if (execution.totalTokens) {
        console.log(`🎯 Tokens used: ${execution.totalTokens.totalTokens} (${execution.totalTokens.inputTokens} input, ${execution.totalTokens.outputTokens} output)`);
      }

      console.log(`📊 Trajectory saved to: ${trajectoryPath}`);

      if (!execution.success) {
        process.exit(1);
      }
    } catch (error) {
      console.error('\n❌ Unexpected error:', error);
      process.exit(1);
    }
  });

/**
 * Interactive command
 */
program
  .command('interactive')
  .description('Start interactive mode')
  .option('-c, --config-file <path>', 'Path to configuration file', 'trae_config.json')
  .option('-p, --provider <provider>', 'LLM provider')
  .option('-m, --model <model>', 'Model name')
  .option('--model-base-url <url>', 'Model base URL')
  .option('--api-key <key>', 'API key')
  .option('--max-steps <number>', 'Maximum number of steps', parseInt)
  .option('-t, --trajectory-file <path>', 'Trajectory file path')
  .action(async (options) => {
    console.log('🎯 Welcome to Trae Agent Interactive Mode!');
    console.log('Type "help" for available commands, "exit" or "quit" to end the session.\n');

    // Create agent
    const agent = createAgent(options.configFile, options);

    // Set up trajectory recording if specified
    let trajectoryPath: string | undefined;
    if (options.trajectoryFile) {
      trajectoryPath = agent.setupTrajectoryRecording(options.trajectoryFile);
      console.log(`📊 Trajectory will be saved to: ${trajectoryPath}\n`);
    }

    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    while (true) {
      try {
        const task = await new Promise<string>((resolve) => {
          rl.question('🤖 Task: ', resolve);
        });

        if (task.toLowerCase() === 'exit' || task.toLowerCase() === 'quit') {
          console.log('👋 Goodbye!');
          break;
        }

        if (task.toLowerCase() === 'help') {
          console.log(`
📚 Available Commands:
  - Type any task description to execute it
  - "status" - Show agent information
  - "clear" - Clear the screen
  - "help" - Show this help message
  - "exit" or "quit" - End the session
`);
          continue;
        }

        if (task.toLowerCase() === 'status') {
          const projectInfo = agent.getProjectInfo();
          console.log(`
📊 Agent Status:
  - Working directory: ${projectInfo.projectPath}
  - Max steps: ${agent['maxSteps']}
  - Model: ${agent['modelParameters'].model}
  - Provider: ${options.provider || 'default'}
`);
          continue;
        }

        if (task.toLowerCase() === 'clear') {
          console.clear();
          continue;
        }

        if (!task.trim()) {
          continue;
        }

        console.log(`\n🚀 Executing task: ${task}`);

        // Execute task
        agent.newTask(task, { project_path: process.cwd() });
        const execution = await agent.executeTask();

        // Display results
        console.log('\n' + '-'.repeat(40));
        if (execution.success) {
          console.log('✅ Task completed!');
        } else {
          console.log('❌ Task failed.');
        }

        if (execution.finalResult) {
          console.log(`📝 ${execution.finalResult}`);
        }

        console.log(`⏱️  Time: ${execution.executionTime.toFixed(2)}s | Steps: ${execution.steps.length}`);
        console.log('-'.repeat(40) + '\n');

      } catch (error) {
        if (error instanceof Error && error.message.includes('SIGINT')) {
          console.log('\n👋 Use "exit" or "quit" to end the session');
        } else {
          console.error('❌ Error:', error);
        }
      }
    }

    rl.close();
  });

/**
 * Show config command
 */
program
  .command('show-config')
  .description('Show current configuration settings')
  .option('-c, --config-file <path>', 'Path to configuration file', 'trae_config.json')
  .action((options) => {
    try {
      const config = loadConfig({ configFile: options.configFile });
      
      console.log('📋 Current Configuration:');
      console.log(`  Default provider: ${config.defaultProvider}`);
      console.log(`  Max steps: ${config.maxSteps}`);
      console.log(`  Enable Lakeview: ${config.enableLakeview}`);
      
      console.log('\n🔧 Model Providers:');
      for (const [provider, params] of Object.entries(config.modelProviders)) {
        console.log(`  ${provider}:`);
        console.log(`    Model: ${params.model}`);
        console.log(`    Max tokens: ${params.maxTokens}`);
        console.log(`    Temperature: ${params.temperature}`);
        if (params.baseUrl) {
          console.log(`    Base URL: ${params.baseUrl}`);
        }
      }
    } catch (error) {
      console.error('❌ Error loading configuration:', error);
      process.exit(1);
    }
  });

/**
 * Tools command
 */
program
  .command('tools')
  .description('Show available tools and their descriptions')
  .action(() => {
    console.log('🛠️  Available Tools:\n');
    
    for (const [toolName, ToolClass] of Object.entries(toolsRegistry)) {
      try {
        const tool = new ToolClass();
        console.log(`📦 ${tool.getName()}`);
        console.log(`   ${tool.getDescription()}\n`);
      } catch (error) {
        console.log(`📦 ${toolName}`);
        console.log(`   ❌ Error loading: ${error}\n`);
      }
    }
  });

// Set up program
program
  .name('trae-cli')
  .description('Trae Agent - LLM-based agent for software engineering tasks')
  .version('0.1.0');

// Parse command line arguments
program.parse();

export default program;
