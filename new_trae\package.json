{"name": "trae-agent-ts", "version": "0.1.0", "description": "TypeScript implementation of Trae Agent - LLM-based agent for general purpose software engineering tasks", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"trae-cli": "dist/cli.js"}, "scripts": {"build": "npm run clean && tsc", "dev": "ts-node src/cli.ts", "start": "node dist/cli.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "npm run build", "example": "ts-node examples/basic-usage.ts"}, "keywords": ["ai", "agent", "llm", "software-engineering", "automation", "typescript"], "author": "Trae Agent Contributors", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.54.0", "@google/generative-ai": "^0.21.0", "@types/sqlite3": "^3.1.11", "axios": "^1.7.0", "commander": "^12.0.0", "dotenv": "^16.4.0", "jsonpath-plus": "^10.0.0", "openai": "^4.86.0", "sqlite3": "^5.1.7", "tree-sitter": "^0.21.0", "tree-sitter-javascript": "^0.21.0", "tree-sitter-python": "^0.21.0", "tree-sitter-typescript": "^0.21.0", "zod": "^3.23.0"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.0", "jest": "^29.7.0", "prettier": "^3.2.0", "rimraf": "^5.0.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.4.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/bytedance/trae-agent.git"}, "bugs": {"url": "https://github.com/bytedance/trae-agent/issues"}, "homepage": "https://github.com/bytedance/trae-agent#readme"}