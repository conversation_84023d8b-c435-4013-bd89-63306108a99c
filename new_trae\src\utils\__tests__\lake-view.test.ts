/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { describe, it, expect, beforeEach } from '@jest/globals';
import { LakeView } from '../lake-view';
import { Config } from '../../types/config';
import { AgentStep } from '../../types/agent';

describe('LakeView', () => {
  let lakeView: LakeView;
  let mockConfig: Config;

  beforeEach(() => {
    mockConfig = {
      defaultProvider: 'openai',
      modelProviders: {
        openai: {
          model: 'gpt-4o',
          baseUrl: 'https://api.openai.com/v1',
          apiKey: 'test-key',
        },
      },
      maxSteps: 20,
      enableLakeview: true,
      lakeviewConfig: {
        enabled: true,
      },
    };

    lakeView = new LakeView(mockConfig);
  });

  describe('summarizeExecution', () => {
    it('should return message for empty steps', () => {
      const result = lakeView.summarizeExecution([]);
      expect(result).toBe('📋 No steps executed');
    });

    it('should summarize execution with steps', () => {
      const steps: AgentStep[] = [
        {
          stepNumber: 1,
          state: 'thinking',
          timestamp: new Date().toISOString(),
        },
        {
          stepNumber: 2,
          state: 'tool_calling',
          timestamp: new Date().toISOString(),
          toolCalls: [
            {
              id: 'test-1',
              name: 'bash',
              arguments: { command: 'ls' },
            },
          ],
        },
        {
          stepNumber: 3,
          state: 'completed',
          timestamp: new Date().toISOString(),
          toolCalls: [
            {
              id: 'test-2',
              name: 'str_replace_based_edit_tool',
              arguments: { path: 'test.ts' },
            },
          ],
        },
      ];

      const result = lakeView.summarizeExecution(steps);
      
      expect(result).toContain('Execution Summary:');
      expect(result).toContain('Steps: 3');
      expect(result).toContain('Tools used: bash, str_replace_based_edit_tool');
      expect(result).toContain('Final state: ✅ completed');
    });

    it('should handle steps without tools', () => {
      const steps: AgentStep[] = [
        {
          stepNumber: 1,
          state: 'thinking',
          timestamp: new Date().toISOString(),
        },
      ];

      const result = lakeView.summarizeExecution(steps);
      
      expect(result).toContain('Tools used: None');
    });
  });

  describe('createExecutionSummary', () => {
    it('should be an alias for summarizeExecution', () => {
      const steps: AgentStep[] = [
        {
          stepNumber: 1,
          state: 'completed',
          timestamp: new Date().toISOString(),
        },
      ];

      const summarizeResult = lakeView.summarizeExecution(steps);
      const createResult = lakeView.createExecutionSummary(steps);
      
      expect(createResult).toBe(summarizeResult);
    });
  });

  describe('createLakeviewStep', () => {
    it('should create lake view step for thinking step', async () => {
      const step: AgentStep = {
        stepNumber: 1,
        state: 'thinking',
        timestamp: new Date().toISOString(),
        llmResponse: {
          content: 'I need to analyze the code structure',
          role: 'assistant',
        },
      };

      const result = await lakeView.createLakeviewStep(step);
      
      expect(result).toBeDefined();
      expect(result!.tagsEmoji).toContain('🧠 THINK');
      expect(result!.descTask).toContain('I need to analyze the code structure');
    });

    it('should create lake view step for tool calling step', async () => {
      const step: AgentStep = {
        stepNumber: 2,
        state: 'tool_calling',
        timestamp: new Date().toISOString(),
        toolCalls: [
          {
            id: 'test-1',
            name: 'str_replace_based_edit_tool',
            arguments: { path: 'test.ts' },
          },
        ],
      };

      const result = await lakeView.createLakeviewStep(step);
      
      expect(result).toBeDefined();
      expect(result!.tagsEmoji).toContain('📝 WRITE_FIX');
      expect(result!.descTask).toContain('str_replace_based_edit_tool');
    });

    it('should handle multiple tools', async () => {
      const step: AgentStep = {
        stepNumber: 3,
        state: 'tool_calling',
        timestamp: new Date().toISOString(),
        toolCalls: [
          {
            id: 'test-1',
            name: 'bash',
            arguments: { command: 'npm test' },
          },
          {
            id: 'test-2',
            name: 'ckg',
            arguments: { command: 'search_function' },
          },
        ],
      };

      const result = await lakeView.createLakeviewStep(step);
      
      expect(result).toBeDefined();
      expect(result!.tagsEmoji).toContain('VERIFY_FIX');
      expect(result!.tagsEmoji).toContain('EXAMINE_CODE');
    });

    it('should handle step with reflection', async () => {
      const step: AgentStep = {
        stepNumber: 4,
        state: 'completed',
        timestamp: new Date().toISOString(),
        reflection: 'Task completed successfully',
      };

      const result = await lakeView.createLakeviewStep(step);
      
      expect(result).toBeDefined();
      expect(result!.descDetails).toBe('Task completed successfully');
    });
  });

  describe('tag generation', () => {
    it('should generate correct tags for different tools', async () => {
      const testCases = [
        {
          toolName: 'str_replace_based_edit_tool',
          expectedTag: '📝 WRITE_FIX',
        },
        {
          toolName: 'bash',
          expectedTag: '⚡ VERIFY_FIX',
        },
        {
          toolName: 'ckg',
          expectedTag: '👁️ EXAMINE_CODE',
        },
        {
          toolName: 'task_done',
          expectedTag: '📣 REPORT',
        },
        {
          toolName: 'sequentialthinking',
          expectedTag: '🧠 THINK',
        },
        {
          toolName: 'unknown_tool',
          expectedTag: '⁉️ OUTLIER',
        },
      ];

      for (const testCase of testCases) {
        const step: AgentStep = {
          stepNumber: 1,
          state: 'tool_calling',
          timestamp: new Date().toISOString(),
          toolCalls: [
            {
              id: 'test-1',
              name: testCase.toolName,
              arguments: {},
            },
          ],
        };

        const result = await lakeView.createLakeviewStep(step);
        expect(result!.tagsEmoji).toContain(testCase.expectedTag);
      }
    });

    it('should detect test-related operations', async () => {
      const step: AgentStep = {
        stepNumber: 1,
        state: 'tool_calling',
        timestamp: new Date().toISOString(),
        toolCalls: [
          {
            id: 'test-1',
            name: 'str_replace_based_edit_tool',
            arguments: { path: 'test.spec.ts' },
          },
        ],
        toolResults: [
          {
            id: 'test-1',
            name: 'str_replace_based_edit_tool',
            result: {
              output: 'Created test file with unit tests',
            },
          },
        ],
      };

      const result = await lakeView.createLakeviewStep(step);
      expect(result!.tagsEmoji).toContain('☑️ WRITE_TEST');
    });

    it('should detect verification operations', async () => {
      const step: AgentStep = {
        stepNumber: 1,
        state: 'tool_calling',
        timestamp: new Date().toISOString(),
        toolCalls: [
          {
            id: 'test-1',
            name: 'bash',
            arguments: { command: 'npm test' },
          },
        ],
        toolResults: [
          {
            id: 'test-1',
            name: 'bash',
            result: {
              output: 'All tests passed',
            },
          },
        ],
      };

      const result = await lakeView.createLakeviewStep(step);
      expect(result!.tagsEmoji).toContain('✅ VERIFY_TEST');
    });
  });

  describe('content analysis', () => {
    it('should analyze LLM response for thinking patterns', async () => {
      const step: AgentStep = {
        stepNumber: 1,
        state: 'thinking',
        timestamp: new Date().toISOString(),
        llmResponse: {
          content: 'I need to write test cases for this function',
          role: 'assistant',
        },
      };

      const result = await lakeView.createLakeviewStep(step);
      expect(result!.tagsEmoji).toContain('☑️ WRITE_TEST');
    });

    it('should analyze for examination patterns', async () => {
      const step: AgentStep = {
        stepNumber: 1,
        state: 'thinking',
        timestamp: new Date().toISOString(),
        llmResponse: {
          content: 'Let me examine the code structure to understand the issue',
          role: 'assistant',
        },
      };

      const result = await lakeView.createLakeviewStep(step);
      expect(result!.tagsEmoji).toContain('👁️ EXAMINE_CODE');
    });
  });
});
