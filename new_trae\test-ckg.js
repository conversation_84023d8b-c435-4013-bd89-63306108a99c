// Simple test for CKG tool functionality
const fs = require('fs');
const path = require('path');

// Test the basic CKG functionality
async function testCKG() {
  console.log('Testing CKG tool basic functionality...');
  
  // Create a simple test directory with some code files
  const testDir = path.join(__dirname, 'test-codebase');
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir);
  }
  
  // Create a simple Python file
  const pythonCode = `
def hello_world():
    """A simple hello world function"""
    print("Hello, World!")
    return "Hello, World!"

class Calculator:
    """A simple calculator class"""
    
    def __init__(self):
        self.result = 0
    
    def add(self, x, y):
        """Add two numbers"""
        self.result = x + y
        return self.result
    
    def multiply(self, x, y):
        """Multiply two numbers"""
        self.result = x * y
        return self.result

if __name__ == "__main__":
    hello_world()
    calc = Calculator()
    print(calc.add(5, 3))
`;
  
  fs.writeFileSync(path.join(testDir, 'example.py'), pythonCode);
  
  // Create a simple JavaScript file
  const jsCode = `
function greet(name) {
    return \`Hello, \${name}!\`;
}

class Person {
    constructor(name, age) {
        this.name = name;
        this.age = age;
    }
    
    introduce() {
        return \`Hi, I'm \${this.name} and I'm \${this.age} years old.\`;
    }
    
    celebrateBirthday() {
        this.age++;
        return \`Happy birthday! Now I'm \${this.age}.\`;
    }
}

module.exports = { greet, Person };
`;
  
  fs.writeFileSync(path.join(testDir, 'example.js'), jsCode);
  
  console.log('Test codebase created successfully!');
  console.log('Files created:');
  console.log('- test-codebase/example.py');
  console.log('- test-codebase/example.js');
  
  console.log('\nTo test the CKG tool, you can now run:');
  console.log('npx trae-cli run "Search for functions in the test codebase using CKG tool"');
}

testCKG().catch(console.error);
