/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { AgentStep, AgentState } from '../types/agent';
import { Config } from '../types/config';

/**
 * Lake View step result
 */
export interface LakeViewStep {
  descTask: string;
  descDetails: string;
  tagsEmoji: string;
}

/**
 * Known tags for Lake View
 */
const KNOWN_TAGS = {
  'WRITE_TEST': '☑️',
  'VERIFY_TEST': '✅',
  'EXAMINE_CODE': '👁️',
  'WRITE_FIX': '📝',
  'VERIFY_FIX': '🔥',
  'REPORT': '📣',
  'THINK': '🧠',
  'OUTLIER': '⁉️',
};

/**
 * Lake View - Provides short and concise summarization for agent steps
 */
export class LakeView {
  private config: Config;

  constructor(config: Config) {
    this.config = config;
  }

  /**
   * Generate a concise summary of an agent step
   */
  summarizeStep(step: AgentStep): string {
    const stateEmoji = this.getStateEmoji(step.state);
    let summary = `${stateEmoji} Step ${step.stepNumber}: `;

    switch (step.state) {
      case AgentState.THINKING:
        summary += 'Analyzing task and planning approach';
        break;
      case AgentState.CALLING_TOOL:
        if (step.toolCalls && step.toolCalls.length > 0) {
          const toolNames = step.toolCalls.map(tc => tc.name).join(', ');
          summary += `Using tools: ${toolNames}`;
        } else {
          summary += 'Executing tools';
        }
        break;
      case AgentState.REFLECTING:
        summary += 'Reflecting on results';
        break;
      case AgentState.COMPLETED:
        summary += 'Task completed successfully';
        break;
      case AgentState.ERROR:
        summary += `Error occurred: ${step.error || 'Unknown error'}`;
        break;
      default:
        summary += step.state;
    }

    return summary;
  }

  /**
   * Generate a summary of the entire execution
   */
  summarizeExecution(steps: AgentStep[]): string {
    if (steps.length === 0) {
      return '📋 No steps executed';
    }

    const lastStep = steps[steps.length - 1];
    const totalSteps = steps.length;
    const toolsUsed = new Set<string>();

    // Collect all tools used
    for (const step of steps) {
      if (step.toolCalls) {
        for (const toolCall of step.toolCalls) {
          toolsUsed.add(toolCall.name);
        }
      }
    }

    let summary = `📊 Execution Summary:\n`;
    summary += `   • Steps: ${totalSteps}\n`;
    summary += `   • Tools used: ${Array.from(toolsUsed).join(', ') || 'None'}\n`;
    summary += `   • Final state: ${this.getStateEmoji(lastStep.state)} ${lastStep.state}`;

    return summary;
  }

  /**
   * Create execution summary (alias for compatibility)
   */
  createExecutionSummary(steps: AgentStep[]): string {
    return this.summarizeExecution(steps);
  }

  /**
   * Create Lake View step (async version)
   */
  async createLakeviewStep(agentStep: AgentStep): Promise<LakeViewStep | null> {
    // Analyze step content for better summarization
    const analysis = this.analyzeStepContent(agentStep);

    return {
      descTask: analysis.task,
      descDetails: analysis.details || agentStep.reflection || '',
      tagsEmoji: this.getStepTags(agentStep),
    };
  }

  /**
   * Create step summary
   */
  private createStepSummary(step: AgentStep): string {
    if (step.toolCalls && step.toolCalls.length > 0) {
      const toolNames = step.toolCalls.map(call => call.name).join(', ');
      return `Using tools: ${toolNames}`;
    }

    if (step.llmResponse?.content) {
      return step.llmResponse.content.substring(0, 100) + '...';
    }

    return `Step ${step.stepNumber}: ${step.state}`;
  }

  /**
   * Get step tags based on tool usage and content analysis
   */
  private getStepTags(step: AgentStep): string {
    if (!step.toolCalls || step.toolCalls.length === 0) {
      // Analyze LLM response content for thinking patterns
      if (step.llmResponse?.content) {
        const content = step.llmResponse.content.toLowerCase();
        if (content.includes('test') && content.includes('write')) {
          return `${KNOWN_TAGS.WRITE_TEST} WRITE_TEST`;
        }
        if (content.includes('examine') || content.includes('analyze') || content.includes('look')) {
          return `${KNOWN_TAGS.EXAMINE_CODE} EXAMINE_CODE`;
        }
      }
      return `${KNOWN_TAGS.THINK} THINK`;
    }

    const toolNames = step.toolCalls.map(call => call.name);
    const tags: string[] = [];

    // Analyze tool usage patterns
    if (toolNames.includes('str_replace_based_edit_tool')) {
      // Check if it's writing tests or fixes
      const toolResults = step.toolResults?.find(r => r.name === 'str_replace_based_edit_tool');
      if (toolResults?.result.output?.toLowerCase().includes('test')) {
        tags.push(`${KNOWN_TAGS.WRITE_TEST} WRITE_TEST`);
      } else {
        tags.push(`${KNOWN_TAGS.WRITE_FIX} WRITE_FIX`);
      }
    }

    if (toolNames.includes('bash')) {
      // Check if it's running tests or verification
      const toolResults = step.toolResults?.find(r => r.name === 'bash');
      if (toolResults?.result.output?.toLowerCase().includes('test')) {
        tags.push(`${KNOWN_TAGS.VERIFY_TEST} VERIFY_TEST`);
      } else {
        tags.push(`${KNOWN_TAGS.VERIFY_FIX} VERIFY_FIX`);
      }
    }

    if (toolNames.includes('ckg')) {
      tags.push(`${KNOWN_TAGS.EXAMINE_CODE} EXAMINE_CODE`);
    }

    if (toolNames.includes('task_done')) {
      tags.push(`${KNOWN_TAGS.REPORT} REPORT`);
    }

    if (toolNames.includes('sequentialthinking')) {
      tags.push(`${KNOWN_TAGS.THINK} THINK`);
    }

    if (tags.length === 0) {
      tags.push(`${KNOWN_TAGS.OUTLIER} OUTLIER`);
    }

    return tags.join(' · ');
  }

  /**
   * Analyze step content for better summarization
   */
  private analyzeStepContent(step: AgentStep): { task: string; details: string } {
    let task = `Step ${step.stepNumber}`;
    let details = '';

    // Analyze tool calls
    if (step.toolCalls && step.toolCalls.length > 0) {
      const toolNames = step.toolCalls.map(call => call.name);

      if (toolNames.includes('str_replace_based_edit_tool')) {
        task = 'Editing files';
        const editResults = step.toolResults?.filter(r => r.name === 'str_replace_based_edit_tool');
        if (editResults && editResults.length > 0) {
          details = `Modified ${editResults.length} file(s)`;
        }
      } else if (toolNames.includes('bash')) {
        task = 'Running commands';
        const bashResults = step.toolResults?.filter(r => r.name === 'bash');
        if (bashResults && bashResults.length > 0) {
          details = `Executed ${bashResults.length} command(s)`;
        }
      } else if (toolNames.includes('ckg')) {
        task = 'Searching codebase';
        details = 'Using code knowledge graph';
      } else if (toolNames.includes('json_edit_tool')) {
        task = 'Editing JSON files';
        details = 'Modifying configuration';
      } else {
        task = `Using ${toolNames.join(', ')}`;
      }
    } else if (step.llmResponse?.content) {
      // Analyze LLM response for task understanding
      const content = step.llmResponse.content;
      if (content.length > 100) {
        task = 'Analyzing and planning';
        details = content.substring(0, 150) + '...';
      } else {
        task = content.substring(0, 50) + (content.length > 50 ? '...' : '');
      }
    }

    return { task, details };
  }

  /**
   * Get emoji for agent state
   */
  private getStateEmoji(state: AgentState): string {
    const emojiMap: Record<AgentState, string> = {
      [AgentState.IDLE]: '⏸️',
      [AgentState.THINKING]: '🤔',
      [AgentState.CALLING_TOOL]: '🔧',
      [AgentState.REFLECTING]: '💭',
      [AgentState.COMPLETED]: '✅',
      [AgentState.ERROR]: '❌',
    };

    return emojiMap[state] || '❓';
  }

  /**
   * Format tool results for display
   */
  formatToolResults(step: AgentStep): string {
    if (!step.toolResults || step.toolResults.length === 0) {
      return '';
    }

    let output = '';
    for (const result of step.toolResults) {
      if (result.result.output) {
        output += `📤 ${result.name}: ${this.truncateText(result.result.output, 100)}\n`;
      }
      if (result.result.error) {
        output += `❌ ${result.name}: ${this.truncateText(result.result.error, 100)}\n`;
      }
    }

    return output.trim();
  }

  /**
   * Truncate text to specified length
   */
  private truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) {
      return text;
    }
    return text.substring(0, maxLength - 3) + '...';
  }
}
