/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { AgentStep, AgentState } from '../types/agent';
import { Config } from '../types/config';

/**
 * Lake View step result
 */
export interface LakeViewStep {
  descTask: string;
  descDetails: string;
  tagsEmoji: string;
}

/**
 * Lake View - Provides short and concise summarization for agent steps
 */
export class LakeView {
  private config: Config;

  constructor(config: Config) {
    this.config = config;
  }

  /**
   * Generate a concise summary of an agent step
   */
  summarizeStep(step: AgentStep): string {
    const stateEmoji = this.getStateEmoji(step.state);
    let summary = `${stateEmoji} Step ${step.stepNumber}: `;

    switch (step.state) {
      case AgentState.THINKING:
        summary += 'Analyzing task and planning approach';
        break;
      case AgentState.CALLING_TOOL:
        if (step.toolCalls && step.toolCalls.length > 0) {
          const toolNames = step.toolCalls.map(tc => tc.name).join(', ');
          summary += `Using tools: ${toolNames}`;
        } else {
          summary += 'Executing tools';
        }
        break;
      case AgentState.REFLECTING:
        summary += 'Reflecting on results';
        break;
      case AgentState.COMPLETED:
        summary += 'Task completed successfully';
        break;
      case AgentState.ERROR:
        summary += `Error occurred: ${step.error || 'Unknown error'}`;
        break;
      default:
        summary += step.state;
    }

    return summary;
  }

  /**
   * Generate a summary of the entire execution
   */
  summarizeExecution(steps: AgentStep[]): string {
    if (steps.length === 0) {
      return '📋 No steps executed';
    }

    const lastStep = steps[steps.length - 1];
    const totalSteps = steps.length;
    const toolsUsed = new Set<string>();

    // Collect all tools used
    for (const step of steps) {
      if (step.toolCalls) {
        for (const toolCall of step.toolCalls) {
          toolsUsed.add(toolCall.name);
        }
      }
    }

    let summary = `📊 Execution Summary:\n`;
    summary += `   • Steps: ${totalSteps}\n`;
    summary += `   • Tools used: ${Array.from(toolsUsed).join(', ') || 'None'}\n`;
    summary += `   • Final state: ${this.getStateEmoji(lastStep.state)} ${lastStep.state}`;

    return summary;
  }

  /**
   * Create execution summary (alias for compatibility)
   */
  createExecutionSummary(steps: AgentStep[]): string {
    return this.summarizeExecution(steps);
  }

  /**
   * Create Lake View step (async version)
   */
  async createLakeviewStep(agentStep: AgentStep): Promise<LakeViewStep | null> {
    // This is a simplified implementation
    // In the full version, this would use LLM to analyze the step
    const stepSummary = this.createStepSummary(agentStep);

    return {
      descTask: stepSummary,
      descDetails: agentStep.reflection || '',
      tagsEmoji: this.getStepTags(agentStep),
    };
  }

  /**
   * Create step summary
   */
  private createStepSummary(step: AgentStep): string {
    if (step.toolCalls && step.toolCalls.length > 0) {
      const toolNames = step.toolCalls.map(call => call.name).join(', ');
      return `Using tools: ${toolNames}`;
    }

    if (step.llmResponse?.content) {
      return step.llmResponse.content.substring(0, 100) + '...';
    }

    return `Step ${step.stepNumber}: ${step.state}`;
  }

  /**
   * Get step tags based on tool usage
   */
  private getStepTags(step: AgentStep): string {
    if (!step.toolCalls || step.toolCalls.length === 0) {
      return '🧠 THINK';
    }

    const toolNames = step.toolCalls.map(call => call.name);

    if (toolNames.includes('str_replace_based_edit_tool')) {
      return '📝 WRITE_FIX';
    }

    if (toolNames.includes('bash')) {
      return '⚡ VERIFY_TEST';
    }

    if (toolNames.includes('ckg')) {
      return '👁️ EXAMINE_CODE';
    }

    if (toolNames.includes('task_done')) {
      return '📣 REPORT';
    }

    return '⁉️ OUTLIER';
  }

  /**
   * Get emoji for agent state
   */
  private getStateEmoji(state: AgentState): string {
    const emojiMap: Record<AgentState, string> = {
      [AgentState.IDLE]: '⏸️',
      [AgentState.THINKING]: '🤔',
      [AgentState.CALLING_TOOL]: '🔧',
      [AgentState.REFLECTING]: '💭',
      [AgentState.COMPLETED]: '✅',
      [AgentState.ERROR]: '❌',
    };

    return emojiMap[state] || '❓';
  }

  /**
   * Format tool results for display
   */
  formatToolResults(step: AgentStep): string {
    if (!step.toolResults || step.toolResults.length === 0) {
      return '';
    }

    let output = '';
    for (const result of step.toolResults) {
      if (result.result.output) {
        output += `📤 ${result.name}: ${this.truncateText(result.result.output, 100)}\n`;
      }
      if (result.result.error) {
        output += `❌ ${result.name}: ${this.truncateText(result.result.error, 100)}\n`;
      }
    }

    return output.trim();
  }

  /**
   * Truncate text to specified length
   */
  private truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) {
      return text;
    }
    return text.substring(0, maxLength - 3) + '...';
  }
}
