/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import * as fs from 'fs';
import * as path from 'path';
import { LLMMessage, LLMResponse } from '../types/llm';
import { AgentStep, AgentState } from '../types/agent';
import { ToolCall, ToolResult } from '../types/tool';
import { DEFAULT_TRAJECTORY_DIR } from './constants';

/**
 * Trajectory data structure
 */
interface TrajectoryData {
  task: string;
  startTime: string;
  endTime: string;
  provider: string;
  model: string;
  maxSteps: number;
  metadata: {
    version: string;
    nodeVersion: string;
    platform: string;
    workingDirectory: string;
    configFile?: string;
  };
  llmInteractions: Array<{
    timestamp: string;
    messages: LLMMessage[];
    response: LLMResponse;
    provider: string;
    model: string;
    tools?: string[];
    tokenUsage?: {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
    };
  }>;
  agentSteps: Array<{
    stepNumber: number;
    timestamp: string;
    state: AgentState;
    llmMessages?: LLMMessage[];
    llmResponse?: LLMResponse;
    toolCalls?: ToolCall[];
    toolResults?: ToolResult[];
    reflection?: string;
    error?: string;
    duration?: number;
  }>;
  success: boolean;
  finalResult?: string;
  executionTime: number;
  totalTokensUsed?: number;
  errorCount: number;
}

/**
 * Records trajectory data for agent execution and LLM interactions
 */
export class TrajectoryRecorder {
  private trajectoryPath: string;
  private trajectoryData: TrajectoryData;
  private startTime?: Date;

  constructor(trajectoryPath?: string) {
    if (!trajectoryPath) {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
      trajectoryPath = path.join(DEFAULT_TRAJECTORY_DIR, `trajectory_${timestamp}.json`);
    }

    this.trajectoryPath = trajectoryPath;
    this.trajectoryData = {
      task: '',
      startTime: '',
      endTime: '',
      provider: '',
      model: '',
      maxSteps: 0,
      metadata: {
        version: '0.1.0',
        nodeVersion: process.version,
        platform: process.platform,
        workingDirectory: process.cwd(),
      },
      llmInteractions: [],
      agentSteps: [],
      success: false,
      finalResult: undefined,
      executionTime: 0,
      totalTokensUsed: 0,
      errorCount: 0,
    };
  }

  /**
   * Get the trajectory file path
   */
  getTrajectoryPath(): string {
    return this.trajectoryPath;
  }

  /**
   * Start recording a new trajectory
   */
  startRecording(task: string, provider: string, model: string, maxSteps: number, configFile?: string): void {
    this.startTime = new Date();
    this.trajectoryData = {
      ...this.trajectoryData,
      task,
      startTime: this.startTime.toISOString(),
      provider,
      model,
      maxSteps,
      metadata: {
        ...this.trajectoryData.metadata,
        configFile,
      },
      llmInteractions: [],
      agentSteps: [],
    };
    this.saveTrajectory();
  }

  /**
   * Record an LLM interaction
   */
  recordLLMInteraction(
    messages: LLMMessage[],
    response: LLMResponse,
    provider: string,
    model: string,
    tools?: string[]
  ): void {
    const tokenUsage = response.usage ? {
      inputTokens: response.usage.inputTokens,
      outputTokens: response.usage.outputTokens,
      totalTokens: response.usage.inputTokens + response.usage.outputTokens,
    } : undefined;

    // Update total token usage
    if (tokenUsage) {
      this.trajectoryData.totalTokensUsed = (this.trajectoryData.totalTokensUsed || 0) + tokenUsage.totalTokens;
    }

    this.trajectoryData.llmInteractions.push({
      timestamp: new Date().toISOString(),
      messages: this.serializeMessages(messages),
      response: this.serializeResponse(response),
      provider,
      model,
      tools,
      tokenUsage,
    });
    this.saveTrajectory();
  }

  /**
   * Record an agent step
   */
  recordAgentStep(
    stepNumber: number,
    state: AgentState,
    llmMessages?: LLMMessage[],
    llmResponse?: LLMResponse,
    toolCalls?: ToolCall[],
    toolResults?: ToolResult[],
    reflection?: string,
    error?: string,
    duration?: number
  ): void {
    // Update error count
    if (error) {
      this.trajectoryData.errorCount++;
    }

    this.trajectoryData.agentSteps.push({
      stepNumber,
      timestamp: new Date().toISOString(),
      state,
      llmMessages: llmMessages ? this.serializeMessages(llmMessages) : undefined,
      llmResponse: llmResponse ? this.serializeResponse(llmResponse) : undefined,
      toolCalls,
      toolResults,
      reflection,
      error,
      duration,
    });
    this.saveTrajectory();
  }

  /**
   * Finalize recording with success status and final result
   */
  finalizeRecording(success: boolean, finalResult?: string): void {
    const endTime = new Date();
    const executionTime = this.startTime ? (endTime.getTime() - this.startTime.getTime()) / 1000 : 0;

    this.trajectoryData = {
      ...this.trajectoryData,
      endTime: endTime.toISOString(),
      success,
      finalResult,
      executionTime,
    };
    this.saveTrajectory();
  }

  /**
   * Save trajectory to file
   */
  private saveTrajectory(): void {
    try {
      // Ensure directory exists
      const dir = path.dirname(this.trajectoryPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      fs.writeFileSync(this.trajectoryPath, JSON.stringify(this.trajectoryData, null, 2));
    } catch (error) {
      console.warn(`Failed to save trajectory to ${this.trajectoryPath}:`, error);
    }
  }

  /**
   * Serialize messages for storage
   */
  private serializeMessages(messages: LLMMessage[]): LLMMessage[] {
    return messages.map(msg => ({
      role: msg.role,
      content: msg.content,
      toolResult: msg.toolResult ? {
        callId: msg.toolResult.callId,
        name: msg.toolResult.name,
        result: msg.toolResult.result,
      } : undefined,
    }));
  }

  /**
   * Serialize response for storage
   */
  private serializeResponse(response: LLMResponse): LLMResponse {
    return {
      content: response.content,
      model: response.model,
      finishReason: response.finishReason,
      usage: response.usage,
      toolCalls: response.toolCalls,
    };
  }
}
