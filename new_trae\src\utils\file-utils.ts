/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import * as fs from 'fs';
import * as path from 'path';
import { SUPPORTED_CODE_EXTENSIONS, MAX_FILE_SIZE } from './constants';

/**
 * File utility functions
 */

/**
 * Check if a file is a supported code file
 */
export function isSupportedCodeFile(filePath: string): boolean {
  const ext = path.extname(filePath).toLowerCase();
  return SUPPORTED_CODE_EXTENSIONS.includes(ext);
}

/**
 * Check if a file is too large to process
 */
export function isFileTooLarge(filePath: string): boolean {
  try {
    const stats = fs.statSync(filePath);
    return stats.size > MAX_FILE_SIZE;
  } catch {
    return false;
  }
}

/**
 * Safely read a file with size and encoding checks
 */
export function safeReadFile(filePath: string): string | null {
  try {
    if (!fs.existsSync(filePath)) {
      return null;
    }

    if (isFileTooLarge(filePath)) {
      throw new Error(`File too large: ${filePath}`);
    }

    return fs.readFileSync(filePath, 'utf-8');
  } catch (error) {
    console.warn(`Failed to read file ${filePath}:`, error);
    return null;
  }
}

/**
 * Safely write a file with directory creation
 */
export function safeWriteFile(filePath: string, content: string): boolean {
  try {
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(filePath, content, 'utf-8');
    return true;
  } catch (error) {
    console.warn(`Failed to write file ${filePath}:`, error);
    return false;
  }
}

/**
 * Get file extension without dot
 */
export function getFileExtension(filePath: string): string {
  return path.extname(filePath).slice(1).toLowerCase();
}

/**
 * Check if path is within allowed directory
 */
export function isPathSafe(filePath: string, allowedRoot: string): boolean {
  const resolvedPath = path.resolve(filePath);
  const resolvedRoot = path.resolve(allowedRoot);
  return resolvedPath.startsWith(resolvedRoot);
}

/**
 * List files in directory recursively with filtering
 */
export function listFiles(
  dirPath: string,
  options: {
    recursive?: boolean;
    maxDepth?: number;
    extensions?: string[];
    excludePatterns?: RegExp[];
  } = {}
): string[] {
  const {
    recursive = false,
    maxDepth = 10,
    extensions,
    excludePatterns = [],
  } = options;

  const files: string[] = [];

  function traverse(currentPath: string, depth: number) {
    if (depth > maxDepth) return;

    try {
      const entries = fs.readdirSync(currentPath, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(currentPath, entry.name);
        const relativePath = path.relative(dirPath, fullPath);

        // Skip hidden files and directories
        if (entry.name.startsWith('.')) continue;

        // Check exclude patterns
        if (excludePatterns.some(pattern => pattern.test(relativePath))) {
          continue;
        }

        if (entry.isDirectory()) {
          if (recursive) {
            traverse(fullPath, depth + 1);
          }
        } else if (entry.isFile()) {
          // Check extensions filter
          if (extensions) {
            const ext = getFileExtension(fullPath);
            if (!extensions.includes(ext)) continue;
          }

          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`Failed to read directory ${currentPath}:`, error);
    }
  }

  traverse(dirPath, 0);
  return files;
}

/**
 * Create a backup of a file
 */
export function createBackup(filePath: string): string | null {
  try {
    if (!fs.existsSync(filePath)) {
      return null;
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${filePath}.backup.${timestamp}`;
    
    fs.copyFileSync(filePath, backupPath);
    return backupPath;
  } catch (error) {
    console.warn(`Failed to create backup for ${filePath}:`, error);
    return null;
  }
}

/**
 * Calculate file hash (simple implementation)
 */
export function calculateFileHash(filePath: string): string | null {
  try {
    const content = fs.readFileSync(filePath);
    const crypto = require('crypto');
    return crypto.createHash('md5').update(content).digest('hex');
  } catch (error) {
    console.warn(`Failed to calculate hash for ${filePath}:`, error);
    return null;
  }
}
