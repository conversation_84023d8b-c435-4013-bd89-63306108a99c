/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { ToolExecutor } from '../../tools/base';
import { TaskDoneTool } from '../../tools/task-done-tool';
import { ToolCall } from '../../types/tool';

describe('ToolExecutor', () => {
  let executor: ToolExecutor;
  let taskDoneTool: TaskDoneTool;

  beforeEach(() => {
    taskDoneTool = new TaskDoneTool();
    executor = new ToolExecutor([taskDoneTool]);
  });

  describe('tool management', () => {
    it('should add tools correctly', () => {
      const tools = executor.getTools();
      expect(tools).toHaveLength(1);
      expect(tools[0]).toBe(taskDoneTool);
    });

    it('should get tool by name', () => {
      const tool = executor.getTool('task_done');
      expect(tool).toBe(taskDoneTool);
    });

    it('should return undefined for unknown tool', () => {
      const tool = executor.getTool('unknown_tool');
      expect(tool).toBeUndefined();
    });

    it('should add new tool', () => {
      const newTool = new TaskDoneTool();
      executor.addTool(newTool);
      
      const tools = executor.getTools();
      expect(tools).toHaveLength(1); // Same name, so replaces
    });

    it('should remove tool', () => {
      executor.removeTool('task_done');
      
      const tools = executor.getTools();
      expect(tools).toHaveLength(0);
    });
  });

  describe('tool execution', () => {
    it('should execute single tool call', async () => {
      const toolCall: ToolCall = {
        callId: 'call_1',
        name: 'task_done',
        arguments: {},
      };

      const results = await executor.sequentialToolCall([toolCall]);
      
      expect(results).toHaveLength(1);
      expect(results[0].callId).toBe('call_1');
      expect(results[0].name).toBe('task_done');
      expect(results[0].result.output).toBe('Task done.');
    });

    it('should handle unknown tool', async () => {
      const toolCall: ToolCall = {
        callId: 'call_1',
        name: 'unknown_tool',
        arguments: {},
      };

      const results = await executor.sequentialToolCall([toolCall]);
      
      expect(results).toHaveLength(1);
      expect(results[0].result.error).toContain('not found');
      expect(results[0].result.errorCode).toBe(-1);
    });

    it('should execute multiple tool calls sequentially', async () => {
      const toolCalls: ToolCall[] = [
        {
          callId: 'call_1',
          name: 'task_done',
          arguments: {},
        },
        {
          callId: 'call_2',
          name: 'task_done',
          arguments: {},
        },
      ];

      const results = await executor.sequentialToolCall(toolCalls);
      
      expect(results).toHaveLength(2);
      expect(results[0].callId).toBe('call_1');
      expect(results[1].callId).toBe('call_2');
    });

    it('should execute multiple tool calls in parallel', async () => {
      const toolCalls: ToolCall[] = [
        {
          callId: 'call_1',
          name: 'task_done',
          arguments: {},
        },
        {
          callId: 'call_2',
          name: 'task_done',
          arguments: {},
        },
      ];

      const results = await executor.parallelToolCall(toolCalls);
      
      expect(results).toHaveLength(2);
      expect(results.map(r => r.callId).sort()).toEqual(['call_1', 'call_2']);
    });
  });
});
