/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import * as sqlite3 from 'sqlite3';
import Parser from 'tree-sitter';
import { Tool } from './base';
import { ToolParameter, ToolCallArguments, ToolExecResult, CKGCommand, FunctionEntry, ClassEntry, CKGStorage } from '../types/tool';

// Tree-sitter language parsers
const TreeSitterJavaScript = require('tree-sitter-javascript');
const TreeSitterPython = require('tree-sitter-python');
const TreeSitterTypeScript = require('tree-sitter-typescript');

const CKG_COMMANDS: CKGCommand[] = ['search_function', 'search_class', 'search_class_method'];

// File extension to language mapping
const EXTENSION_TO_LANGUAGE: Record<string, string> = {
  '.py': 'python',
  '.js': 'javascript',
  '.ts': 'typescript',
  '.tsx': 'typescript',
  '.jsx': 'javascript',
  '.java': 'java',
  '.cpp': 'cpp',
  '.c': 'c',
  '.h': 'c',
};

// Language to parser mapping
const LANGUAGE_TO_PARSER: Record<string, any> = {
  javascript: TreeSitterJavaScript,
  typescript: TreeSitterTypeScript.typescript,
  python: TreeSitterPython,
};

const MAX_RESPONSE_LEN = 8000;
const CKG_DATABASE_PATH = path.join(process.cwd(), '.trae', 'ckg');
const CKG_DATABASE_EXPIRY_TIME = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

/**
 * Get CKG database path for a specific codebase snapshot
 */
function getCKGDatabasePath(codebaseSnapshotHash: string): string {
  return path.join(CKG_DATABASE_PATH, `ckg_${codebaseSnapshotHash}.db`);
}

/**
 * Get folder snapshot hash to detect changes
 */
function getFolderSnapshotHash(folderPath: string): string {
  const hash = crypto.createHash('md5');
  
  function processDirectory(dirPath: string) {
    const items = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const item of items) {
      if (item.name.startsWith('.')) continue;
      
      const fullPath = path.join(dirPath, item.name);
      
      if (item.isDirectory()) {
        processDirectory(fullPath);
      } else if (item.isFile()) {
        const stat = fs.statSync(fullPath);
        hash.update(item.name);
        hash.update(stat.mtime.toString());
        hash.update(stat.size.toString());
      }
    }
  }
  
  processDirectory(folderPath);
  return hash.digest('hex');
}

/**
 * Initialize CKG database
 */
function initializeDB(codebaseSnapshotHash: string): sqlite3.Database {
  if (!fs.existsSync(CKG_DATABASE_PATH)) {
    fs.mkdirSync(CKG_DATABASE_PATH, { recursive: true });
  }
  
  const databasePath = getCKGDatabasePath(codebaseSnapshotHash);
  const db = new sqlite3.Database(databasePath);
  
  // Create functions table
  db.run(`
    CREATE TABLE IF NOT EXISTS functions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      file_path TEXT NOT NULL,
      body TEXT NOT NULL,
      start_line INTEGER NOT NULL,
      end_line INTEGER NOT NULL
    )
  `);
  
  // Create classes table
  db.run(`
    CREATE TABLE IF NOT EXISTS classes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      file_path TEXT NOT NULL,
      body TEXT NOT NULL,
      fields TEXT NOT NULL,
      methods TEXT NOT NULL,
      start_line INTEGER NOT NULL,
      end_line INTEGER NOT NULL
    )
  `);
  
  // Create class_methods table
  db.run(`
    CREATE TABLE IF NOT EXISTS class_methods (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      class_name TEXT NOT NULL,
      file_path TEXT NOT NULL,
      body TEXT NOT NULL,
      start_line INTEGER NOT NULL,
      end_line INTEGER NOT NULL
    )
  `);
  
  return db;
}

/**
 * Parse a single file and extract functions and classes
 */
function parseFile(filePath: string, language: string): { functions: FunctionEntry[], classes: ClassEntry[] } {
  const functions: FunctionEntry[] = [];
  const classes: ClassEntry[] = [];
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const parser = new Parser();
    
    const languageParser = LANGUAGE_TO_PARSER[language];
    if (!languageParser) {
      return { functions, classes };
    }
    
    parser.setLanguage(languageParser);
    const tree = parser.parse(content);
    
    function traverse(node: any) {
      if (node.type === 'function_definition' || node.type === 'function_declaration' || node.type === 'method_definition') {
        const nameNode = node.childForFieldName('name');
        if (nameNode) {
          const functionName = nameNode.text;
          const startLine = node.startPosition.row + 1;
          const endLine = node.endPosition.row + 1;
          const body = content.slice(node.startIndex, node.endIndex);
          
          functions.push({
            name: functionName,
            filePath,
            body,
            startLine,
            endLine,
          });
        }
      } else if (node.type === 'class_definition' || node.type === 'class_declaration') {
        const nameNode = node.childForFieldName('name');
        if (nameNode) {
          const className = nameNode.text;
          const startLine = node.startPosition.row + 1;
          const endLine = node.endPosition.row + 1;
          const body = content.slice(node.startIndex, node.endIndex);
          
          // Extract methods and fields (simplified)
          const methods: string[] = [];
          const fields: string[] = [];
          
          function extractClassMembers(classNode: any) {
            for (let i = 0; i < classNode.childCount; i++) {
              const child = classNode.child(i);
              if (child.type === 'method_definition' || child.type === 'function_definition') {
                const methodNameNode = child.childForFieldName('name');
                if (methodNameNode) {
                  methods.push(methodNameNode.text);
                }
              }
              // Add field extraction logic here if needed
            }
          }
          
          extractClassMembers(node);
          
          classes.push({
            name: className,
            filePath,
            body,
            fields,
            methods,
            startLine,
            endLine,
          });
        }
      }
      
      for (let i = 0; i < node.childCount; i++) {
        traverse(node.child(i));
      }
    }
    
    traverse(tree.rootNode);
  } catch (error) {
    console.warn(`Failed to parse file ${filePath}:`, error);
  }
  
  return { functions, classes };
}

/**
 * Construct CKG for a codebase
 */
function constructCKG(db: sqlite3.Database, codebasePath: string): void {
  function processDirectory(dirPath: string) {
    const items = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const item of items) {
      if (item.name.startsWith('.')) continue;
      
      const fullPath = path.join(dirPath, item.name);
      
      if (item.isDirectory()) {
        processDirectory(fullPath);
      } else if (item.isFile()) {
        const ext = path.extname(item.name);
        const language = EXTENSION_TO_LANGUAGE[ext];
        
        if (language && LANGUAGE_TO_PARSER[language]) {
          const { functions, classes } = parseFile(fullPath, language);
          
          // Insert functions
          const insertFunction = db.prepare(`
            INSERT INTO functions (name, file_path, body, start_line, end_line)
            VALUES (?, ?, ?, ?, ?)
          `);
          
          for (const func of functions) {
            insertFunction.run(func.name, func.filePath, func.body, func.startLine, func.endLine);
          }
          insertFunction.finalize();
          
          // Insert classes
          const insertClass = db.prepare(`
            INSERT INTO classes (name, file_path, body, fields, methods, start_line, end_line)
            VALUES (?, ?, ?, ?, ?, ?, ?)
          `);
          
          for (const cls of classes) {
            insertClass.run(
              cls.name,
              cls.filePath,
              cls.body,
              JSON.stringify(cls.fields),
              JSON.stringify(cls.methods),
              cls.startLine,
              cls.endLine
            );
            
            // Insert class methods
            const insertMethod = db.prepare(`
              INSERT INTO class_methods (name, class_name, file_path, body, start_line, end_line)
              VALUES (?, ?, ?, ?, ?, ?)
            `);
            
            for (const method of cls.methods) {
              // This is simplified - in a real implementation, you'd extract method bodies
              insertMethod.run(method, cls.name, cls.filePath, '', cls.startLine, cls.endLine);
            }
            insertMethod.finalize();
          }
          insertClass.finalize();
        }
      }
    }
  }
  
  processDirectory(codebasePath);
}

/**
 * Clear older CKG databases
 */
export function clearOlderCKG(): void {
  if (!fs.existsSync(CKG_DATABASE_PATH)) return;

  const files = fs.readdirSync(CKG_DATABASE_PATH);
  const now = Date.now();

  for (const file of files) {
    if (file.startsWith('ckg_') && file.endsWith('.db')) {
      const filePath = path.join(CKG_DATABASE_PATH, file);
      const stat = fs.statSync(filePath);

      if (now - stat.mtime.getTime() > CKG_DATABASE_EXPIRY_TIME) {
        fs.unlinkSync(filePath);
      }
    }
  }
}

/**
 * CKG (Code Knowledge Graph) Tool
 */
export class CKGTool extends Tool {
  private ckgPath: Map<string, CKGStorage> = new Map();

  constructor(modelProvider?: string) {
    super(modelProvider);
  }

  getName(): string {
    return 'ckg';
  }

  getDescription(): string {
    return `Query the code knowledge graph of a codebase.
* State is persistent across command calls and discussions with the user
* The \`search_function\` command searches for functions in the codebase
* The \`search_class\` command searches for classes in the codebase
* The \`search_class_method\` command searches for class methods in the codebase
* If a \`command\` generates a long output, it will be truncated and marked with \`<response clipped>\`
* If multiple entries are found, the tool will return all of them until the truncation is reached.
* By default, the tool will print function or class bodies as well as the file path and line number of the function or class. You can disable this by setting the \`print_body\` parameter to \`false\`.`;
  }

  getParameters(): ToolParameter[] {
    return [
      {
        name: 'command',
        type: 'string',
        description: `The command to run. Allowed options are ${CKG_COMMANDS.join(', ')}.`,
        required: true,
        enum: CKG_COMMANDS,
      },
      {
        name: 'path',
        type: 'string',
        description: 'The path to the codebase.',
        required: true,
      },
      {
        name: 'identifier',
        type: 'string',
        description: 'The identifier of the function or class to search for in the code knowledge graph.',
        required: true,
      },
      {
        name: 'print_body',
        type: 'boolean',
        description: 'Whether to print the body of the function or class. This is enabled by default.',
        required: false,
      },
    ];
  }

  async execute(args: ToolCallArguments): Promise<ToolExecResult> {
    const command = args['command'] as CKGCommand;
    const pathArg = args['path'] as string;
    const identifier = args['identifier'] as string;
    const printBody = args['print_body'] !== false; // Default to true

    if (!command) {
      return {
        error: `No command provided for the ${this.getName()} tool`,
        errorCode: -1,
      };
    }

    if (!pathArg) {
      return {
        error: `No path provided for the ${this.getName()} tool`,
        errorCode: -1,
      };
    }

    if (!identifier) {
      return {
        error: `No identifier provided for the ${this.getName()} tool`,
        errorCode: -1,
      };
    }

    if (!fs.existsSync(pathArg)) {
      return {
        error: `Codebase path ${pathArg} does not exist`,
        errorCode: -1,
      };
    }

    if (!fs.statSync(pathArg).isDirectory()) {
      return {
        error: `Codebase path ${pathArg} is not a directory`,
        errorCode: -1,
      };
    }

    const ckgConnection = this.getOrConstructCKG(pathArg);

    switch (command) {
      case 'search_function':
        return {
          output: await this.searchFunction(ckgConnection, identifier, printBody),
        };
      case 'search_class':
        return {
          output: await this.searchClass(ckgConnection, identifier, printBody),
        };
      case 'search_class_method':
        return {
          output: await this.searchClassMethod(ckgConnection, identifier, printBody),
        };
      default:
        return {
          error: `Invalid command: ${command}`,
          errorCode: -1,
        };
    }
  }

  private getOrConstructCKG(codebasePath: string): sqlite3.Database {
    const codebaseSnapshotHash = getFolderSnapshotHash(codebasePath);

    if (!this.ckgPath.has(codebasePath)) {
      // No previous hash, check if a previously built CKG exists
      const dbPath = getCKGDatabasePath(codebaseSnapshotHash);
      if (fs.existsSync(dbPath)) {
        const dbConnection = new sqlite3.Database(dbPath);
        this.ckgPath.set(codebasePath, {
          dbConnection,
          codebaseSnapshotHash,
        });
        return dbConnection;
      } else {
        const dbConnection = initializeDB(codebaseSnapshotHash);
        constructCKG(dbConnection, codebasePath);
        this.ckgPath.set(codebasePath, {
          dbConnection,
          codebaseSnapshotHash,
        });
        return dbConnection;
      }
    } else {
      // Check if codebase has changed
      const storage = this.ckgPath.get(codebasePath)!;
      if (storage.codebaseSnapshotHash !== codebaseSnapshotHash) {
        // Codebase has changed, rebuild CKG
        storage.dbConnection.close();
        const oldDbPath = getCKGDatabasePath(storage.codebaseSnapshotHash);
        if (fs.existsSync(oldDbPath)) {
          fs.unlinkSync(oldDbPath);
        }

        const dbConnection = initializeDB(codebaseSnapshotHash);
        constructCKG(dbConnection, codebasePath);
        this.ckgPath.set(codebasePath, {
          dbConnection,
          codebaseSnapshotHash,
        });
        return dbConnection;
      }
      return storage.dbConnection;
    }
  }

  private async searchFunction(db: sqlite3.Database, identifier: string, printBody: boolean = true): Promise<string> {
    return new Promise<string>((resolve) => {
      const query = `
        SELECT name, file_path, body, start_line, end_line
        FROM functions
        WHERE name LIKE ?
        ORDER BY name
      `;

      db.all(query, [`%${identifier}%`], (err, rows: any[]) => {
        if (err) {
          resolve(`Error searching functions: ${err.message}`);
          return;
        }

        if (rows.length === 0) {
          resolve(`No functions found matching "${identifier}"`);
          return;
        }

        let result = `Found ${rows.length} function(s) matching "${identifier}":\n\n`;
        let totalLength = result.length;

        for (const row of rows) {
          let entry = `Function: ${row.name}\n`;
          entry += `File: ${row.file_path}\n`;
          entry += `Lines: ${row.start_line}-${row.end_line}\n`;

          if (printBody) {
            entry += `Body:\n${row.body}\n`;
          }
          entry += '\n' + '-'.repeat(50) + '\n\n';

          if (totalLength + entry.length > MAX_RESPONSE_LEN) {
            result += '<response clipped>';
            break;
          }

          result += entry;
          totalLength += entry.length;
        }

        resolve(result);
      });
    });
  }

  private async searchClass(db: sqlite3.Database, identifier: string, printBody: boolean = true): Promise<string> {
    return new Promise<string>((resolve) => {
      const query = `
        SELECT name, file_path, body, fields, methods, start_line, end_line
        FROM classes
        WHERE name LIKE ?
        ORDER BY name
      `;

      db.all(query, [`%${identifier}%`], (err, rows: any[]) => {
        if (err) {
          resolve(`Error searching classes: ${err.message}`);
          return;
        }

        if (rows.length === 0) {
          resolve(`No classes found matching "${identifier}"`);
          return;
        }

        let result = `Found ${rows.length} class(es) matching "${identifier}":\n\n`;
        let totalLength = result.length;

        for (const row of rows) {
          let entry = `Class: ${row.name}\n`;
          entry += `File: ${row.file_path}\n`;
          entry += `Lines: ${row.start_line}-${row.end_line}\n`;

          const fields = JSON.parse(row.fields || '[]');
          const methods = JSON.parse(row.methods || '[]');

          if (fields.length > 0) {
            entry += `Fields: ${fields.join(', ')}\n`;
          }
          if (methods.length > 0) {
            entry += `Methods: ${methods.join(', ')}\n`;
          }

          if (printBody) {
            entry += `Body:\n${row.body}\n`;
          }
          entry += '\n' + '-'.repeat(50) + '\n\n';

          if (totalLength + entry.length > MAX_RESPONSE_LEN) {
            result += '<response clipped>';
            break;
          }

          result += entry;
          totalLength += entry.length;
        }

        resolve(result);
      });
    });
  }

  private async searchClassMethod(db: sqlite3.Database, identifier: string, printBody: boolean = true): Promise<string> {
    return new Promise<string>((resolve) => {
      const query = `
        SELECT name, class_name, file_path, body, start_line, end_line
        FROM class_methods
        WHERE name LIKE ?
        ORDER BY class_name, name
      `;

      db.all(query, [`%${identifier}%`], (err, rows: any[]) => {
        if (err) {
          resolve(`Error searching class methods: ${err.message}`);
          return;
        }

        if (rows.length === 0) {
          resolve(`No class methods found matching "${identifier}"`);
          return;
        }

        let result = `Found ${rows.length} class method(s) matching "${identifier}":\n\n`;
        let totalLength = result.length;

        for (const row of rows) {
          let entry = `Method: ${row.class_name}.${row.name}\n`;
          entry += `File: ${row.file_path}\n`;
          entry += `Lines: ${row.start_line}-${row.end_line}\n`;

          if (printBody && row.body) {
            entry += `Body:\n${row.body}\n`;
          }
          entry += '\n' + '-'.repeat(50) + '\n\n';

          if (totalLength + entry.length > MAX_RESPONSE_LEN) {
            result += '<response clipped>';
            break;
          }

          result += entry;
          totalLength += entry.length;
        }

        resolve(result);
      });
    });
  }
}
