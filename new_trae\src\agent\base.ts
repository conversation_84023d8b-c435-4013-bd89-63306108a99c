/**
 * Copyright (c) 2025 ByteDance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

import { LLMMessage, LLMResponse, LLMUsage, ModelParameters } from '../types/llm';
import { AgentExecution, AgentState, AgentStep, AgentError } from '../types/agent';
import { Too<PERSON><PERSON><PERSON>, ToolResult } from '../types/tool';
import { Tool, ToolExecutor } from '../tools/base';
import { LLMClient } from '../utils/llm-client';
import { TrajectoryRecorder } from '../utils/trajectory-recorder';
import { Config } from '../types/config';

/**
 * Base class for LLM-based agents
 */
export abstract class Agent {
  protected llmClient: LLMClient;
  protected modelParameters: ModelParameters;
  protected maxSteps: number;

  protected initialMessages: LLMMessage[] = [];
  protected task = '';
  protected tools: Tool[] = [];
  protected toolCaller: ToolExecutor = new ToolExecutor([]);
  protected trajectoryRecorder?: TrajectoryRecorder;

  constructor(config?: Config, llmClient?: LLMClient) {
    if (llmClient) {
      this.llmClient = llmClient;
      this.modelParameters = llmClient.getModelParameters();
      this.maxSteps = llmClient.getMaxSteps();
    } else if (config) {
      const { LLMClient: LLMClientClass, LLMProvider } = require('../utils/llm-client');
      const provider = LLMProvider[config.defaultProvider.toUpperCase() as keyof typeof LLMProvider];
      const modelParams = config.modelProviders[config.defaultProvider];
      
      this.llmClient = new LLMClientClass(provider, modelParams, config.maxSteps);
      this.modelParameters = modelParams;
      this.maxSteps = config.maxSteps;
    } else {
      throw new Error('Either config or llmClient must be provided');
    }
  }

  /**
   * Create an agent instance from a configuration object
   */
  static fromConfig(config: Config): Agent {
    // This should be implemented by subclasses
    throw new Error('fromConfig must be implemented by subclasses');
  }

  /**
   * Set the trajectory recorder
   */
  setTrajectoryRecorder(recorder?: TrajectoryRecorder): void {
    this.trajectoryRecorder = recorder;
    this.llmClient.setTrajectoryRecorder(recorder);
  }

  /**
   * Create a new task
   */
  abstract newTask(
    task: string,
    extraArgs?: Record<string, string>,
    toolNames?: string[]
  ): void;

  /**
   * Execute a task using the agent
   */
  async executeTask(): Promise<AgentExecution> {
    const startTime = Date.now();
    const execution: AgentExecution = {
      task: this.task,
      steps: [],
      success: false,
      executionTime: 0,
    };

    let step: AgentStep | undefined;

    try {
      let messages = [...this.initialMessages];
      let stepNumber = 1;

      while (stepNumber <= this.maxSteps) {
        step = {
          stepNumber,
          state: AgentState.THINKING,
        };

        try {
          step.state = AgentState.THINKING;

          // Get LLM response
          const llmResponse = await this.llmClient.chat(
            messages,
            this.modelParameters,
            this.tools
          );
          step.llmResponse = llmResponse;

          // Update token usage
          this.updateLLMUsage(llmResponse, execution);

          if (this.llmIndicatesTaskCompleted(llmResponse)) {
            if (this.isTaskCompleted(llmResponse)) {
              this.llmCompleteResponseTaskHandler(llmResponse, step, execution, messages);
              break;
            } else {
              step.state = AgentState.THINKING;
              messages = [
                {
                  role: 'user',
                  content: this.taskIncompleteMessage(),
                },
              ];
            }
          } else {
            // Check if the response contains a tool call
            const toolCalls = llmResponse.toolCalls;
            if (toolCalls && toolCalls.length > 0) {
              messages = await this.toolCallHandler(toolCalls, step);
            } else {
              // Add assistant response to conversation
              messages.push({
                role: 'assistant',
                content: llmResponse.content,
              });
            }
          }

          // Record agent step
          this.recordHandler(step, messages);

          execution.steps.push(step);
          stepNumber++;
        } catch (error) {
          step.state = AgentState.ERROR;
          step.error = error instanceof Error ? error.message : String(error);
          execution.steps.push(step);
          throw error;
        }
      }

      if (stepNumber > this.maxSteps) {
        execution.finalResult = `Agent reached maximum steps (${this.maxSteps}) without completion.`;
        execution.success = false;
      }
    } catch (error) {
      execution.finalResult = `Agent execution failed: ${error instanceof Error ? error.message : String(error)}`;
      execution.success = false;
    }

    execution.executionTime = (Date.now() - startTime) / 1000;
    return execution;
  }

  /**
   * Check if the LLM indicates that the task is completed
   */
  abstract llmIndicatesTaskCompleted(llmResponse: LLMResponse): boolean;

  /**
   * Check if the task is actually completed
   */
  protected isTaskCompleted(_llmResponse: LLMResponse): boolean {
    return true;
  }

  /**
   * Return a message indicating that the task is incomplete
   */
  protected taskIncompleteMessage(): string {
    return 'The task is not yet complete. Please continue working on it.';
  }

  /**
   * Handle LLM completion response
   */
  protected llmCompleteResponseTaskHandler(
    _llmResponse: LLMResponse,
    step: AgentStep,
    execution: AgentExecution,
    _messages: LLMMessage[]
  ): void {
    step.state = AgentState.COMPLETED;
    execution.success = true;
    execution.finalResult = 'Task completed successfully.';
  }

  /**
   * Handle tool calls
   */
  protected async toolCallHandler(toolCalls: ToolCall[], step: AgentStep): Promise<LLMMessage[]> {
    const messages: LLMMessage[] = [];

    step.state = AgentState.CALLING_TOOL;
    step.toolCalls = toolCalls;

    let toolResults: ToolResult[];
    if (this.modelParameters.parallelToolCalls) {
      toolResults = await this.toolCaller.parallelToolCall(toolCalls);
    } else {
      toolResults = await this.toolCaller.sequentialToolCall(toolCalls);
    }

    step.toolResults = toolResults;

    for (const toolResult of toolResults) {
      // Add tool result to conversation
      messages.push({
        role: 'user',
        toolResult,
      });
    }

    const reflection = this.reflectOnResult(toolResults);
    if (reflection) {
      step.state = AgentState.REFLECTING;
      step.reflection = reflection;

      messages.push({
        role: 'assistant',
        content: reflection,
      });
    }

    return messages;
  }

  /**
   * Reflect on tool results
   */
  protected reflectOnResult(_toolResults: ToolResult[]): string | undefined {
    return undefined;
  }

  /**
   * Record handler for trajectory recording
   */
  protected recordHandler(step: AgentStep, messages: LLMMessage[]): void {
    if (this.trajectoryRecorder) {
      this.trajectoryRecorder.recordAgentStep(
        step.stepNumber,
        step.state,
        messages,
        step.llmResponse,
        step.toolCalls,
        step.toolResults,
        step.reflection,
        step.error
      );
    }
  }

  /**
   * Update LLM usage statistics
   */
  protected updateLLMUsage(llmResponse: LLMResponse, execution: AgentExecution): void {
    if (llmResponse.usage) {
      if (!execution.totalTokens) {
        execution.totalTokens = {
          inputTokens: 0,
          outputTokens: 0,
          totalTokens: 0,
        };
      }

      execution.totalTokens.inputTokens += llmResponse.usage.inputTokens;
      execution.totalTokens.outputTokens += llmResponse.usage.outputTokens;
      execution.totalTokens.totalTokens += llmResponse.usage.totalTokens;
    }
  }
}
